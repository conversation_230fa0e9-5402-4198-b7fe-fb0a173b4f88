# Local development configuration for Y.js WebSocket Server
# This file contains local development settings and can be used with docker-compose

version: '3.8'

services:
  y-websocket-server-dev:
    build:
      context: .
      target: development
    container_name: y-websocket-server-dev
    restart: unless-stopped
    ports:
      - "1234:1234"
      - "9229:9229"  # Node.js debugger port
    environment:
      - NODE_ENV=development
      - HOST=0.0.0.0
      - PORT=1234
      - LOG_LEVEL=debug
      - LOG_FORMAT=simple
      - YPERSISTENCE=/app/data
      - GC=true
      - METRICS_ENABLED=true
      - HEALTH_CHECK_ENABLED=true
      - JWT_SECRET=dev-secret-key-change-in-production
      - CORS_ORIGIN=http://localhost:3000
      - AUTH_ENABLED=false
    volumes:
      - .:/app
      - /app/node_modules
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - y-websocket-dev-network
    command: ["dumb-init", "npm", "run", "dev"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1234/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for development and testing
  redis-dev:
    image: redis:7-alpine
    container_name: y-websocket-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - y-websocket-dev-network
    command: redis-server --appendonly yes

  # PostgreSQL for development and testing
  postgres-dev:
    image: postgres:15-alpine
    container_name: y-websocket-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: yjs_websocket_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - y-websocket-dev-network

  # Prometheus for metrics collection in development
  prometheus-dev:
    image: prom/prometheus:latest
    container_name: y-websocket-prometheus-dev
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-dev.yml:/etc/prometheus/prometheus.yml
      - prometheus_dev_data:/prometheus
    networks:
      - y-websocket-dev-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=24h'
      - '--web.enable-lifecycle'

  # Grafana for metrics visualization in development
  grafana-dev:
    image: grafana/grafana:latest
    container_name: y-websocket-grafana-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_dev_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - y-websocket-dev-network

  # Test client for development
  test-client:
    build:
      context: ./test-client
      dockerfile: Dockerfile
    container_name: y-websocket-test-client
    restart: "no"
    environment:
      - WEBSOCKET_URL=ws://y-websocket-server-dev:1234
      - DOCUMENT_NAME=test-document
    networks:
      - y-websocket-dev-network
    depends_on:
      - y-websocket-server-dev
    profiles:
      - testing

networks:
  y-websocket-dev-network:
    driver: bridge

volumes:
  redis_dev_data:
  postgres_dev_data:
  prometheus_dev_data:
  grafana_dev_data:
