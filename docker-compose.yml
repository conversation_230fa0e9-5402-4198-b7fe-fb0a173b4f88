version: '3.8'

services:
  y-websocket-server:
    build:
      context: .
      target: production
    container_name: y-websocket-server
    restart: unless-stopped
    ports:
      - "1234:1234"
    environment:
      - NODE_ENV=production
      - HOST=0.0.0.0
      - PORT=1234
      - LOG_LEVEL=info
      - LOG_FORMAT=json
      - YPERSISTENCE=/app/data
      - GC=true
      - METRICS_ENABLED=true
      - HEALTH_CHECK_ENABLED=true
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - y-websocket-network
    healthcheck:
      test: ["CMD", "node", "-e", "const http = require('http'); const options = { hostname: 'localhost', port: 1234, path: '/health', timeout: 2000 }; const req = http.request(options, (res) => { if (res.statusCode === 200) process.exit(0); else process.exit(1); }); req.on('error', () => process.exit(1)); req.on('timeout', () => process.exit(1)); req.end();"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for scaling (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: y-websocket-redis
  #   restart: unless-stopped
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - y-websocket-network
  #   command: redis-server --appendonly yes

  # Optional: PostgreSQL for external persistence (uncomment if needed)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: y-websocket-postgres
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: yjs_websocket
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - y-websocket-network

  # Optional: Prometheus for metrics collection (uncomment if needed)
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: y-websocket-prometheus
  #   restart: unless-stopped
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   networks:
  #     - y-websocket-network
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'
  #     - '--storage.tsdb.retention.time=200h'
  #     - '--web.enable-lifecycle'

  # Optional: Grafana for metrics visualization (uncomment if needed)
  # grafana:
  #   image: grafana/grafana:latest
  #   container_name: y-websocket-grafana
  #   restart: unless-stopped
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #     - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
  #     - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
  #   networks:
  #     - y-websocket-network

networks:
  y-websocket-network:
    driver: bridge

volumes:
  # redis_data:
  # postgres_data:
  # prometheus_data:
  # grafana_data:
