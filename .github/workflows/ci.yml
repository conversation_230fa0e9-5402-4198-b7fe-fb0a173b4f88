name: CI

on:
  push:
    branches:
      - master
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    branches:
      - '**'
    paths-ignore:
      - 'dist/**'

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        node-version: [18.x, 16.x, 14.x]
    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: 📥 Install
        run: npm install

      - name: Unit tests
        run: npm test

      - name: ESLint checks
        run: npm run lint

      - name: Build
        run: npm run dist
      
      - name: Preversion
        run: npm run preversion