{"timestamp":"2025-07-04T20:33:00.249Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T20:33:00.266Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T20:33:00.268Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T20:33:00.280Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":44631}}
{"timestamp":"2025-07-04T20:35:52.277Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"test-document","totalDocuments":1}}
{"timestamp":"2025-07-04T20:35:52.277Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:35:52.278Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:35:57.283Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","duration":5006,"totalConnections":0}}
