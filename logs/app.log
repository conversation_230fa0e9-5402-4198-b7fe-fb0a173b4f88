{"timestamp":"2025-07-04T20:33:00.249Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T20:33:00.266Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T20:33:00.268Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T20:33:00.280Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":44631}}
{"timestamp":"2025-07-04T20:35:52.277Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"test-document","totalDocuments":1}}
{"timestamp":"2025-07-04T20:35:52.277Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:35:52.278Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:35:57.283Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","duration":5006,"totalConnections":0}}
{"timestamp":"2025-07-04T20:36:50.187Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T20:36:50.198Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T20:36:50.200Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T20:36:50.209Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":50232}}
{"timestamp":"2025-07-04T20:56:41.118Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"test-document","totalDocuments":1}}
{"timestamp":"2025-07-04T20:56:41.120Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:56:41.121Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"********************************","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:56:46.121Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"test-document","duration":5001,"totalConnections":0}}
{"timestamp":"2025-07-04T21:07:50.654Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:07:50.666Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:07:50.667Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:07:50.675Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":1505}}
{"timestamp":"2025-07-04T21:07:50.835Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:50.836Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:07:53.446Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:53.446Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:07:55.998Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:55.999Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:07:58.606Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:58.606Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:01.185Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:01.186Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:03.950Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:03.950Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:06.777Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:06.778Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:09.593Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:09.593Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:12.618Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:12.619Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:16.303Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:16.304Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:19.383Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:19.383Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:23.372Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:23.372Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
