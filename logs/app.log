{"timestamp":"2025-07-04T20:33:00.249Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T20:33:00.266Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T20:33:00.268Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T20:33:00.280Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":44631}}
{"timestamp":"2025-07-04T20:35:52.277Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"test-document","totalDocuments":1}}
{"timestamp":"2025-07-04T20:35:52.277Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:35:52.278Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:35:57.283Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ffcd07c0ed1373ffff8c2a67044ec02","docName":"test-document","duration":5006,"totalConnections":0}}
{"timestamp":"2025-07-04T20:36:50.187Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T20:36:50.198Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T20:36:50.200Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T20:36:50.209Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":50232}}
{"timestamp":"2025-07-04T20:56:41.118Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"test-document","totalDocuments":1}}
{"timestamp":"2025-07-04T20:56:41.120Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:56:41.121Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"********************************","docName":"test-document","totalConnections":1}}
{"timestamp":"2025-07-04T20:56:46.121Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"test-document","duration":5001,"totalConnections":0}}
{"timestamp":"2025-07-04T21:07:50.654Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:07:50.666Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:07:50.667Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:07:50.675Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":1505}}
{"timestamp":"2025-07-04T21:07:50.835Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:50.836Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:07:53.446Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:53.446Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:07:55.998Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:55.999Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:07:58.606Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:07:58.606Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:01.185Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:01.186Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:03.950Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:03.950Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:06.777Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:06.778Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:09.593Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:09.593Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:12.618Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:12.619Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:16.303Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:16.304Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:19.383Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:19.383Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:23.372Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:08:23.372Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:08:37.411Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:08:37.422Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:08:37.424Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:08:37.433Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":2952}}
{"timestamp":"2025-07-04T21:08:38.353Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"simple-test-doc","totalDocuments":1}}
{"timestamp":"2025-07-04T21:08:38.355Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b8f7c03845a62c7d125549ff16919ae0","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:08:38.376Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"b8f7c03845a62c7d125549ff16919ae0","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:08:41.205Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"363e28e836d1bcf3b390fc0ff3daed78","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:08:41.210Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"363e28e836d1bcf3b390fc0ff3daed78","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:08:42.119Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b8f7c03845a62c7d125549ff16919ae0","docName":"simple-test-doc","duration":3764,"totalConnections":1}}
{"timestamp":"2025-07-04T21:08:42.260Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"abd404d6c88c2684a25e8e7b63db6979","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:08:42.261Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"abd404d6c88c2684a25e8e7b63db6979","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:08:58.619Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"eb46640729691f98a4396dc9e0241d27","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:08:58.620Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"eb46640729691f98a4396dc9e0241d27","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:08:58.624Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"eb46640729691f98a4396dc9e0241d27","docName":"simple-test-doc","duration":5,"totalConnections":2}}
{"timestamp":"2025-07-04T21:08:58.632Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2c337fe666b6911b8739f56f81f3731d","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:08:58.633Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2c337fe666b6911b8739f56f81f3731d","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:08:58.932Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"319a747f6b4050b9634d76509030d6c8","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:08:58.935Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"319a747f6b4050b9634d76509030d6c8","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:09:00.074Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e9e348430c7a682d1d7e9f4807d218d9","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:09:00.075Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e9e348430c7a682d1d7e9f4807d218d9","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:09:04.826Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7f3f2f34ebac82bd1b2a8bd2bf9f3f15","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:04.827Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"7f3f2f34ebac82bd1b2a8bd2bf9f3f15","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:12.360Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"abd404d6c88c2684a25e8e7b63db6979","docName":"simple-test-doc","duration":30100,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:13.371Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4f5ffe9050d03e733382fb99b76b892c","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:13.372Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4f5ffe9050d03e733382fb99b76b892c","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:31.623Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2c337fe666b6911b8739f56f81f3731d","docName":"simple-test-doc","duration":32991,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:31.729Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1e5322373e0d3e562e30a47789d89034","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:31.729Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1e5322373e0d3e562e30a47789d89034","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:37.555Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1e5322373e0d3e562e30a47789d89034","docName":"simple-test-doc","duration":5826,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:37.641Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e05b2525c6e6765e94951a5de76c485d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:37.642Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e05b2525c6e6765e94951a5de76c485d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:37.643Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e05b2525c6e6765e94951a5de76c485d","docName":"simple-test-doc","duration":2,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:37.654Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7163ee5f59d08b87695bd9f1ab967cf3","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:37.654Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"7163ee5f59d08b87695bd9f1ab967cf3","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:37.805Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7f3f2f34ebac82bd1b2a8bd2bf9f3f15","docName":"simple-test-doc","duration":32979,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:37.910Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d4609b88d27a7fa5a03be33ec70db3a9","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:37.911Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d4609b88d27a7fa5a03be33ec70db3a9","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.006Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7163ee5f59d08b87695bd9f1ab967cf3","docName":"simple-test-doc","duration":1352,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:39.060Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6a7dfaed678e85560cacae34dfd88fdf","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.061Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6a7dfaed678e85560cacae34dfd88fdf","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.064Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6a7dfaed678e85560cacae34dfd88fdf","docName":"simple-test-doc","duration":4,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:39.073Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1a0aa90e2753e4dcadbef56f7ff57d39","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.073Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1a0aa90e2753e4dcadbef56f7ff57d39","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.772Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1a0aa90e2753e4dcadbef56f7ff57d39","docName":"simple-test-doc","duration":699,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:39.826Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0a53347f197d2af6536f5a77b3194c99","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.827Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0a53347f197d2af6536f5a77b3194c99","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.834Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0a53347f197d2af6536f5a77b3194c99","docName":"simple-test-doc","duration":8,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:39.837Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"cfd258e61ac9a71542f153b968777ec3","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:39.838Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"cfd258e61ac9a71542f153b968777ec3","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:09:42.391Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"319a747f6b4050b9634d76509030d6c8","docName":"simple-test-doc","duration":43459,"totalConnections":5}}
{"timestamp":"2025-07-04T21:09:42.406Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e9e348430c7a682d1d7e9f4807d218d9","docName":"simple-test-doc","duration":42332,"totalConnections":4}}
{"timestamp":"2025-07-04T21:09:42.407Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"363e28e836d1bcf3b390fc0ff3daed78","docName":"simple-test-doc","duration":61202,"totalConnections":3}}
{"timestamp":"2025-07-04T21:09:45.220Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"cfd258e61ac9a71542f153b968777ec3","docName":"simple-test-doc","duration":5383,"totalConnections":2}}
{"timestamp":"2025-07-04T21:09:45.270Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b24efdc829892de0b435fdc1256cdf91","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:45.271Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"b24efdc829892de0b435fdc1256cdf91","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:45.274Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b24efdc829892de0b435fdc1256cdf91","docName":"simple-test-doc","duration":3,"totalConnections":2}}
{"timestamp":"2025-07-04T21:09:45.283Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:45.284Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:45.359Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4f5ffe9050d03e733382fb99b76b892c","docName":"simple-test-doc","duration":31988,"totalConnections":2}}
{"timestamp":"2025-07-04T21:09:46.026Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","duration":743,"totalConnections":1}}
{"timestamp":"2025-07-04T21:09:46.092Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0cc666ee1f5c430746bb4fd4aedba843","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:09:46.093Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0cc666ee1f5c430746bb4fd4aedba843","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:09:46.096Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0cc666ee1f5c430746bb4fd4aedba843","docName":"simple-test-doc","duration":4,"totalConnections":1}}
{"timestamp":"2025-07-04T21:09:46.104Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"85b2914586bf99661c1793b408ed4852","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:09:46.104Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"85b2914586bf99661c1793b408ed4852","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:09:46.373Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"49f4b00c3d80156ac706fdca482f142a","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:46.373Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"49f4b00c3d80156ac706fdca482f142a","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:46.450Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"85b2914586bf99661c1793b408ed4852","docName":"simple-test-doc","duration":346,"totalConnections":2}}
{"timestamp":"2025-07-04T21:09:46.493Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3b622e30f525b5498abf5b1d806863fd","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:46.494Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"3b622e30f525b5498abf5b1d806863fd","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:46.497Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3b622e30f525b5498abf5b1d806863fd","docName":"simple-test-doc","duration":4,"totalConnections":2}}
{"timestamp":"2025-07-04T21:09:46.505Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6c98b6686e857267731e7efff6f28176","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:46.506Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6c98b6686e857267731e7efff6f28176","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:47.751Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d4609b88d27a7fa5a03be33ec70db3a9","docName":"simple-test-doc","duration":9841,"totalConnections":2}}
{"timestamp":"2025-07-04T21:09:47.840Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"95c9af04f8eb138b0b8e96cfa89c6f84","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:47.842Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"95c9af04f8eb138b0b8e96cfa89c6f84","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:47.846Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"95c9af04f8eb138b0b8e96cfa89c6f84","docName":"simple-test-doc","duration":6,"totalConnections":2}}
{"timestamp":"2025-07-04T21:09:47.850Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e8bbb751e42d926a52afe150956c705e","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:09:47.850Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e8bbb751e42d926a52afe150956c705e","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:10:19.494Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6c98b6686e857267731e7efff6f28176","docName":"simple-test-doc","duration":32989,"totalConnections":2}}
{"timestamp":"2025-07-04T21:10:19.601Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bf8eb9117f359a12d326bb33d61a1f66","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:10:19.602Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"bf8eb9117f359a12d326bb33d61a1f66","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:10:20.841Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e8bbb751e42d926a52afe150956c705e","docName":"simple-test-doc","duration":32991,"totalConnections":2}}
{"timestamp":"2025-07-04T21:10:20.941Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3f6706f4dfd54ae88e90ae808850f6d4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:10:20.941Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"3f6706f4dfd54ae88e90ae808850f6d4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:10:42.375Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"30d7cc7e66695a587e72123b9774d7bf","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:10:42.381Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"30d7cc7e66695a587e72123b9774d7bf","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:10:42.383Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"49f4b00c3d80156ac706fdca482f142a","docName":"simple-test-doc","duration":56010,"totalConnections":3}}
{"timestamp":"2025-07-04T21:10:42.387Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4214d5c260f131108bb010b3833b2a0d","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:10:42.388Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4214d5c260f131108bb010b3833b2a0d","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:10:42.400Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4161ada6edf07325e52981325dc8ed64","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:10:42.401Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4161ada6edf07325e52981325dc8ed64","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:10:52.495Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bf8eb9117f359a12d326bb33d61a1f66","docName":"simple-test-doc","duration":32894,"totalConnections":4}}
{"timestamp":"2025-07-04T21:10:52.604Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"396f99881c3c1ff247b830f04fef3cdd","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:10:52.607Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"396f99881c3c1ff247b830f04fef3cdd","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:10:53.842Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3f6706f4dfd54ae88e90ae808850f6d4","docName":"simple-test-doc","duration":32901,"totalConnections":4}}
{"timestamp":"2025-07-04T21:10:53.958Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"225503ab3ae9c39069f2ff3f5de2b57f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:10:53.959Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"225503ab3ae9c39069f2ff3f5de2b57f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:11:25.493Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"396f99881c3c1ff247b830f04fef3cdd","docName":"simple-test-doc","duration":32890,"totalConnections":4}}
{"timestamp":"2025-07-04T21:11:25.605Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ff047825c5c99194322d785f1fce2386","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:11:25.606Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ff047825c5c99194322d785f1fce2386","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:11:26.842Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"225503ab3ae9c39069f2ff3f5de2b57f","docName":"simple-test-doc","duration":32884,"totalConnections":4}}
{"timestamp":"2025-07-04T21:11:26.944Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"048640a1adbd101f01b8e380fadfc2df","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:11:26.947Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"048640a1adbd101f01b8e380fadfc2df","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:11:42.377Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"30d7cc7e66695a587e72123b9774d7bf","docName":"simple-test-doc","duration":60003,"totalConnections":4}}
{"timestamp":"2025-07-04T21:11:42.380Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"58d2f6f8ae760b9e4691e981f4c3cac3","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:11:42.381Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"58d2f6f8ae760b9e4691e981f4c3cac3","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:11:42.382Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4214d5c260f131108bb010b3833b2a0d","docName":"simple-test-doc","duration":59995,"totalConnections":4}}
{"timestamp":"2025-07-04T21:11:42.386Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4161ada6edf07325e52981325dc8ed64","docName":"simple-test-doc","duration":59986,"totalConnections":3}}
{"timestamp":"2025-07-04T21:11:56.360Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"048640a1adbd101f01b8e380fadfc2df","docName":"simple-test-doc","duration":29416,"totalConnections":2}}
{"timestamp":"2025-07-04T21:11:56.576Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ef9c53532fbdd4188f4e13002e9cce8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:11:56.580Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2ef9c53532fbdd4188f4e13002e9cce8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:11:58.494Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ff047825c5c99194322d785f1fce2386","docName":"simple-test-doc","duration":32889,"totalConnections":2}}
{"timestamp":"2025-07-04T21:11:58.604Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"713abb22a9284755b5ca0217b096c384","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:11:58.608Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"713abb22a9284755b5ca0217b096c384","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:26.856Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2ef9c53532fbdd4188f4e13002e9cce8","docName":"simple-test-doc","duration":30280,"totalConnections":2}}
{"timestamp":"2025-07-04T21:12:27.081Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c098739700963ab09a1d08a1bb5c6623","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:27.083Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c098739700963ab09a1d08a1bb5c6623","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:31.502Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"713abb22a9284755b5ca0217b096c384","docName":"simple-test-doc","duration":32891,"totalConnections":2}}
{"timestamp":"2025-07-04T21:12:31.602Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0b3b828fe62c5ab78fe243829e55c058","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:31.607Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0b3b828fe62c5ab78fe243829e55c058","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:34.186Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c098739700963ab09a1d08a1bb5c6623","docName":"simple-test-doc","duration":7105,"totalConnections":2}}
{"timestamp":"2025-07-04T21:12:34.400Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"eb51f34ce6d5815fba6125e7d4b1f859","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:34.401Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"eb51f34ce6d5815fba6125e7d4b1f859","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:34.401Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"eb51f34ce6d5815fba6125e7d4b1f859","docName":"simple-test-doc","duration":1,"totalConnections":2}}
{"timestamp":"2025-07-04T21:12:34.413Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"be3f66e459b2261b955098d606fa0d94","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:34.417Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"be3f66e459b2261b955098d606fa0d94","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:42.205Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"be3f66e459b2261b955098d606fa0d94","docName":"simple-test-doc","duration":7792,"totalConnections":2}}
{"timestamp":"2025-07-04T21:12:42.366Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"58d2f6f8ae760b9e4691e981f4c3cac3","docName":"simple-test-doc","duration":59986,"totalConnections":1}}
{"timestamp":"2025-07-04T21:12:42.372Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7c1b1837906d4e848d611834ca63c438","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:12:42.373Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"7c1b1837906d4e848d611834ca63c438","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:12:42.384Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"823635d74a0d9e602f12607de101e740","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:42.389Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"823635d74a0d9e602f12607de101e740","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:12:42.409Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6c0096888a0c960d68043e44e926f577","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:12:42.410Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6c0096888a0c960d68043e44e926f577","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:12:42.416Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ce138831e3d97dc247e60c1a2cd43c33","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:12:42.417Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ce138831e3d97dc247e60c1a2cd43c33","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:04.495Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0b3b828fe62c5ab78fe243829e55c058","docName":"simple-test-doc","duration":32893,"totalConnections":4}}
{"timestamp":"2025-07-04T21:13:04.603Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"04c38a248034e0903436af6357bf2b0f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:04.608Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"04c38a248034e0903436af6357bf2b0f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:15.408Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ce138831e3d97dc247e60c1a2cd43c33","docName":"simple-test-doc","duration":32992,"totalConnections":4}}
{"timestamp":"2025-07-04T21:13:15.512Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"afdae9b68331845e5c53eeba377af65d","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:15.513Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"afdae9b68331845e5c53eeba377af65d","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:22.621Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"afdae9b68331845e5c53eeba377af65d","docName":"simple-test-doc","duration":7109,"totalConnections":4}}
{"timestamp":"2025-07-04T21:13:22.846Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2b2252a92c79a020abbb411b3580891c","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:22.846Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2b2252a92c79a020abbb411b3580891c","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:37.496Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"04c38a248034e0903436af6357bf2b0f","docName":"simple-test-doc","duration":32893,"totalConnections":4}}
{"timestamp":"2025-07-04T21:13:37.612Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c320765d699724569deb83ac9cbd02d9","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:37.615Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c320765d699724569deb83ac9cbd02d9","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:42.396Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7c1b1837906d4e848d611834ca63c438","docName":"simple-test-doc","duration":60024,"totalConnections":4}}
{"timestamp":"2025-07-04T21:13:42.398Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2418162fc946b9afadd535c87670e7ef","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:42.398Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2418162fc946b9afadd535c87670e7ef","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:13:42.401Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"823635d74a0d9e602f12607de101e740","docName":"simple-test-doc","duration":60017,"totalConnections":4}}
{"timestamp":"2025-07-04T21:13:42.406Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6c0096888a0c960d68043e44e926f577","docName":"simple-test-doc","duration":59997,"totalConnections":3}}
{"timestamp":"2025-07-04T21:13:54.616Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e8394466965e777de8f958ac905771ba","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:13:54.617Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e8394466965e777de8f958ac905771ba","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:13:55.826Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2b2252a92c79a020abbb411b3580891c","docName":"simple-test-doc","duration":32980,"totalConnections":3}}
{"timestamp":"2025-07-04T21:13:55.933Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5fb6de3344e6178b3a5ca13f9f34bdd1","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:13:55.934Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"5fb6de3344e6178b3a5ca13f9f34bdd1","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:10.528Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c320765d699724569deb83ac9cbd02d9","docName":"simple-test-doc","duration":32916,"totalConnections":3}}
{"timestamp":"2025-07-04T21:14:10.602Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"549226cb00d1b9ef23a2fc288da9f93f","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:10.602Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"549226cb00d1b9ef23a2fc288da9f93f","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:16.629Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5fb6de3344e6178b3a5ca13f9f34bdd1","docName":"simple-test-doc","duration":20696,"totalConnections":3}}
{"timestamp":"2025-07-04T21:14:16.864Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"012f3c84d78ce269c8f6818c7708c513","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:16.866Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"012f3c84d78ce269c8f6818c7708c513","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:16.868Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"012f3c84d78ce269c8f6818c7708c513","docName":"simple-test-doc","duration":4,"totalConnections":3}}
{"timestamp":"2025-07-04T21:14:16.877Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d958f23a655a8fbe68ff2229c311a240","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:16.879Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d958f23a655a8fbe68ff2229c311a240","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:23.624Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2418162fc946b9afadd535c87670e7ef","docName":"simple-test-doc","duration":41226,"totalConnections":3}}
{"timestamp":"2025-07-04T21:14:23.628Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d958f23a655a8fbe68ff2229c311a240","docName":"simple-test-doc","duration":6751,"totalConnections":2}}
{"timestamp":"2025-07-04T21:14:23.730Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"133661fd50861e1847b3965bb04d21b1","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:14:23.732Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"133661fd50861e1847b3965bb04d21b1","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:14:26.515Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"133661fd50861e1847b3965bb04d21b1","docName":"simple-test-doc","duration":2784,"totalConnections":2}}
{"timestamp":"2025-07-04T21:14:26.537Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2e3f84c8f67c34f74883031e7dc1512e","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:14:26.540Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2e3f84c8f67c34f74883031e7dc1512e","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:14:26.552Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9cd4f5886e0635e9ab1880e6e070f7ed","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:26.553Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9cd4f5886e0635e9ab1880e6e070f7ed","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:14:26.572Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2890d9f566cd030094ea353440de37d5","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:14:26.573Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2890d9f566cd030094ea353440de37d5","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:14:27.823Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9cd4f5886e0635e9ab1880e6e070f7ed","docName":"simple-test-doc","duration":1271,"totalConnections":4}}
{"timestamp":"2025-07-04T21:14:27.823Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2890d9f566cd030094ea353440de37d5","docName":"simple-test-doc","duration":1251,"totalConnections":3}}
{"timestamp":"2025-07-04T21:14:27.823Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2e3f84c8f67c34f74883031e7dc1512e","docName":"simple-test-doc","duration":1286,"totalConnections":2}}
{"timestamp":"2025-07-04T21:14:35.402Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"549226cb00d1b9ef23a2fc288da9f93f","docName":"simple-test-doc","duration":24800,"totalConnections":1}}
{"timestamp":"2025-07-04T21:14:35.586Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"221afc2e5065c6f2e18e8f2c8f144e49","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:14:35.586Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"221afc2e5065c6f2e18e8f2c8f144e49","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:14:35.587Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"221afc2e5065c6f2e18e8f2c8f144e49","docName":"simple-test-doc","duration":1,"totalConnections":1}}
{"timestamp":"2025-07-04T21:14:35.607Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9f1af7b8d738f6f59c85630644873db7","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:14:35.612Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9f1af7b8d738f6f59c85630644873db7","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:14:36.902Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9f1af7b8d738f6f59c85630644873db7","docName":"simple-test-doc","duration":1294,"totalConnections":1}}
{"timestamp":"2025-07-04T21:14:49.811Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"084062aff4623364a580c66230a6a5c4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:14:49.812Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"084062aff4623364a580c66230a6a5c4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:14:49.815Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"084062aff4623364a580c66230a6a5c4","docName":"simple-test-doc","duration":4,"totalConnections":1}}
{"timestamp":"2025-07-04T21:14:49.827Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0c1178c1bb53db859a09994726140df5","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:14:49.828Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0c1178c1bb53db859a09994726140df5","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:15:07.435Z","level":"info","message":"Cleaning up stale connections","service":"y-websocket-server","component":"ConnectionManager","metadata":{"count":1}}
{"timestamp":"2025-07-04T21:15:07.436Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e8394466965e777de8f958ac905771ba","docName":"simple-test-doc","duration":72820,"totalConnections":1}}
{"timestamp":"2025-07-04T21:15:22.812Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0c1178c1bb53db859a09994726140df5","docName":"simple-test-doc","duration":32985,"totalConnections":0}}
{"timestamp":"2025-07-04T21:15:22.922Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d4ae35218cd149fb9799abf52f897f19","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:15:22.923Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d4ae35218cd149fb9799abf52f897f19","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:15:55.811Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d4ae35218cd149fb9799abf52f897f19","docName":"simple-test-doc","duration":32889,"totalConnections":0}}
{"timestamp":"2025-07-04T21:15:55.918Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a22af70f7782a39b1fe85f1abd5558fb","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:15:55.919Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"a22af70f7782a39b1fe85f1abd5558fb","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:16:28.810Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a22af70f7782a39b1fe85f1abd5558fb","docName":"simple-test-doc","duration":32892,"totalConnections":0}}
{"timestamp":"2025-07-04T21:16:28.923Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d06ae3e6c696b72319b5b410765c15f2","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:16:28.924Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d06ae3e6c696b72319b5b410765c15f2","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:17:01.811Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d06ae3e6c696b72319b5b410765c15f2","docName":"simple-test-doc","duration":32887,"totalConnections":0}}
{"timestamp":"2025-07-04T21:17:01.921Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"af8cda391861eb7280e23038185fe99b","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:17:01.923Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"af8cda391861eb7280e23038185fe99b","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:17:34.810Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"af8cda391861eb7280e23038185fe99b","docName":"simple-test-doc","duration":32889,"totalConnections":0}}
{"timestamp":"2025-07-04T21:17:34.925Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b1d05840a81e9d49eaf7cd8d358a951b","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:17:34.926Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"b1d05840a81e9d49eaf7cd8d358a951b","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:17:53.378Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5fa34cc26a0c16dea95c6fce3cae9ec9","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:17:53.407Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"5fa34cc26a0c16dea95c6fce3cae9ec9","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:17:53.411Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b1d05840a81e9d49eaf7cd8d358a951b","docName":"simple-test-doc","duration":18486,"totalConnections":1}}
{"timestamp":"2025-07-04T21:18:08.117Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6eee579e7d06c5f4807ca585c1f68145","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:08.133Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6eee579e7d06c5f4807ca585c1f68145","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:08.135Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5fa34cc26a0c16dea95c6fce3cae9ec9","docName":"simple-test-doc","duration":14758,"totalConnections":1}}
{"timestamp":"2025-07-04T21:18:23.005Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31f8f5356e594cdbec0a46d33317ab97","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:23.018Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"31f8f5356e594cdbec0a46d33317ab97","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:23.032Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6eee579e7d06c5f4807ca585c1f68145","docName":"simple-test-doc","duration":14916,"totalConnections":1}}
{"timestamp":"2025-07-04T21:18:38.836Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"71472ff66c17b897ce84a1dda68d91a4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:38.839Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"71472ff66c17b897ce84a1dda68d91a4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:38.841Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31f8f5356e594cdbec0a46d33317ab97","docName":"simple-test-doc","duration":15837,"totalConnections":1}}
{"timestamp":"2025-07-04T21:18:48.782Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fc921d6bb94684ed0390970ba579849a","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:48.783Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"fc921d6bb94684ed0390970ba579849a","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:48.787Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fc921d6bb94684ed0390970ba579849a","docName":"simple-test-doc","duration":5,"totalConnections":1}}
{"timestamp":"2025-07-04T21:18:48.809Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c6108a1054d86f4dacf9b484bd2c1e3f","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:18:48.815Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c6108a1054d86f4dacf9b484bd2c1e3f","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:12.306Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"71472ff66c17b897ce84a1dda68d91a4","docName":"simple-test-doc","duration":33461,"totalConnections":1}}
{"timestamp":"2025-07-04T21:19:22.603Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"290f344ffea25599d9d879bb6caa80c5","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:22.604Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"290f344ffea25599d9d879bb6caa80c5","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:22.605Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"290f344ffea25599d9d879bb6caa80c5","docName":"simple-test-doc","duration":2,"totalConnections":1}}
{"timestamp":"2025-07-04T21:19:22.616Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9e05c8e6aee8f26e2fff464d4044aab1","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:22.632Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9e05c8e6aee8f26e2fff464d4044aab1","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:28.455Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c6108a1054d86f4dacf9b484bd2c1e3f","docName":"simple-test-doc","duration":39647,"totalConnections":1}}
{"timestamp":"2025-07-04T21:19:28.640Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f7f2f1d3d55abb2f4c628b562374ea16","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:28.643Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"f7f2f1d3d55abb2f4c628b562374ea16","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:28.644Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f7f2f1d3d55abb2f4c628b562374ea16","docName":"simple-test-doc","duration":4,"totalConnections":1}}
{"timestamp":"2025-07-04T21:19:28.654Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"682ae62f5f0b9be0d07fa2c1ccf0a64a","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:28.657Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"682ae62f5f0b9be0d07fa2c1ccf0a64a","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:19:40.033Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"140fb4a5a85d250ca4b3822fd252f738","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:19:40.035Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"140fb4a5a85d250ca4b3822fd252f738","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:19:40.035Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"140fb4a5a85d250ca4b3822fd252f738","docName":"simple-test-doc","duration":2,"totalConnections":2}}
{"timestamp":"2025-07-04T21:19:40.043Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"aa5b6b6294959f552242b6d51f19fca3","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:19:40.044Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"aa5b6b6294959f552242b6d51f19fca3","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:19:46.507Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9e05c8e6aee8f26e2fff464d4044aab1","docName":"simple-test-doc","duration":23890,"totalConnections":2}}
{"timestamp":"2025-07-04T21:20:01.649Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"682ae62f5f0b9be0d07fa2c1ccf0a64a","docName":"simple-test-doc","duration":32994,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:01.749Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"21ece834045083beb7a8faf7bdfa2132","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:01.749Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"21ece834045083beb7a8faf7bdfa2132","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:11.552Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"aa5b6b6294959f552242b6d51f19fca3","docName":"simple-test-doc","duration":31509,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:11.827Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"91218ff5d9a529533f73c6c8ece03539","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:11.828Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"91218ff5d9a529533f73c6c8ece03539","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:11.830Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"91218ff5d9a529533f73c6c8ece03539","docName":"simple-test-doc","duration":3,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:11.840Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bbe43459597a7e6a0906a6225ed6593c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:11.844Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"bbe43459597a7e6a0906a6225ed6593c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:29.596Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bbe43459597a7e6a0906a6225ed6593c","docName":"simple-test-doc","duration":17756,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:29.748Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"84be81f70aa3ad5d79ea5e8a8b27735f","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:29.749Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"84be81f70aa3ad5d79ea5e8a8b27735f","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:29.751Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"84be81f70aa3ad5d79ea5e8a8b27735f","docName":"simple-test-doc","duration":3,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:29.759Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"57531d72dd547f051c6308992e314809","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:29.760Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"57531d72dd547f051c6308992e314809","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:33.322Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"57531d72dd547f051c6308992e314809","docName":"simple-test-doc","duration":3564,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:33.396Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d40a376f94486ad5ac941ac68e204074","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:33.398Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d40a376f94486ad5ac941ac68e204074","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:33.399Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d40a376f94486ad5ac941ac68e204074","docName":"simple-test-doc","duration":3,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:33.408Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2fe8950f885a36720dc1ec62b6bf6f39","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:33.409Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2fe8950f885a36720dc1ec62b6bf6f39","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:33.933Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2fe8950f885a36720dc1ec62b6bf6f39","docName":"simple-test-doc","duration":525,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:34.000Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"da35fb42d342cd5fb12e560b87fe3eb1","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:34.001Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"da35fb42d342cd5fb12e560b87fe3eb1","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:34.004Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"da35fb42d342cd5fb12e560b87fe3eb1","docName":"simple-test-doc","duration":4,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:34.013Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c39c6503dc587bea58feff9bed174a24","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:34.014Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c39c6503dc587bea58feff9bed174a24","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:34.620Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c39c6503dc587bea58feff9bed174a24","docName":"simple-test-doc","duration":607,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:34.640Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"21ece834045083beb7a8faf7bdfa2132","docName":"simple-test-doc","duration":32891,"totalConnections":0}}
{"timestamp":"2025-07-04T21:20:34.704Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"733c5e281572dbfd067f825b0cc9da4a","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:20:34.704Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"733c5e281572dbfd067f825b0cc9da4a","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:20:34.708Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"733c5e281572dbfd067f825b0cc9da4a","docName":"simple-test-doc","duration":4,"totalConnections":0}}
{"timestamp":"2025-07-04T21:20:34.721Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:20:34.722Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:20:34.742Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4c36403a763e8972c15cd881cfa9bb6f","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:34.743Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4c36403a763e8972c15cd881cfa9bb6f","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:39.102Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4c36403a763e8972c15cd881cfa9bb6f","docName":"simple-test-doc","duration":4360,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:39.196Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1631f6e09affa978a93cf3b73b99f064","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:39.197Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1631f6e09affa978a93cf3b73b99f064","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:39.200Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1631f6e09affa978a93cf3b73b99f064","docName":"simple-test-doc","duration":4,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:39.207Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"12ddd7ab4e196b80258147d20ede2b3d","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:39.209Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"12ddd7ab4e196b80258147d20ede2b3d","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:43.497Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","duration":8776,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:43.599Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"01b0f7a4c5f1958741dd36a84ca5a477","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:43.599Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"01b0f7a4c5f1958741dd36a84ca5a477","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:43.603Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"01b0f7a4c5f1958741dd36a84ca5a477","docName":"simple-test-doc","duration":4,"totalConnections":1}}
{"timestamp":"2025-07-04T21:20:43.615Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fb059e6447fd606c4a24c006b36c33e0","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:20:43.616Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"fb059e6447fd606c4a24c006b36c33e0","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:21:12.200Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"12ddd7ab4e196b80258147d20ede2b3d","docName":"simple-test-doc","duration":32992,"totalConnections":1}}
{"timestamp":"2025-07-04T21:21:12.303Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"61911117b66838c97b028d6ac17a56c7","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:21:12.303Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"61911117b66838c97b028d6ac17a56c7","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:21:16.605Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fb059e6447fd606c4a24c006b36c33e0","docName":"simple-test-doc","duration":32990,"totalConnections":1}}
{"timestamp":"2025-07-04T21:21:16.701Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2a7e4a6a3f874d37637711c396cfb9a4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:21:16.703Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2a7e4a6a3f874d37637711c396cfb9a4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:21:45.199Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"61911117b66838c97b028d6ac17a56c7","docName":"simple-test-doc","duration":32896,"totalConnections":1}}
{"timestamp":"2025-07-04T21:21:45.308Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c35245aeb019347531ef2a2c948ccc2e","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:21:45.309Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c35245aeb019347531ef2a2c948ccc2e","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:21:49.595Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2a7e4a6a3f874d37637711c396cfb9a4","docName":"simple-test-doc","duration":32893,"totalConnections":1}}
{"timestamp":"2025-07-04T21:21:49.704Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:21:49.916Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:21:50.324Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:21:51.131Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:21:52.741Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:21:55.251Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:21:57.760Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:00.266Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:02.773Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:05.283Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:07.791Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:10.307Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:12.814Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:15.326Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:16.362Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:16.371Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:16.575Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:16.981Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:17.785Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:18.197Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c35245aeb019347531ef2a2c948ccc2e","docName":"simple-test-doc","duration":32889,"totalConnections":0}}
{"timestamp":"2025-07-04T21:22:18.300Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:18.505Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:18.911Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:19.401Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:19.715Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:21.320Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:21.910Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:23.827Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:24.417Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:26.332Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:26.933Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:28.838Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:29.441Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:31.346Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:31.952Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:33.853Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:34.463Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:36.358Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:38.864Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:41.378Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:43.886Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:46.393Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:48.903Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:51.408Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:53.914Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:56.420Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:22:58.932Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:01.439Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:03.945Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:06.452Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:08.957Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:11.462Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:13.970Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:16.475Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:18.986Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:21.494Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:24.001Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:26.508Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:29.015Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:31.524Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:34.032Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:36.540Z","level":"warn","message":"Rate limit exceeded","service":"y-websocket-server","component":"RateLimiter","metadata":{"key":"::1","requestCount":100,"maxRequests":100,"windowMs":900000}}
{"timestamp":"2025-07-04T21:23:39.051Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"caaee647ec6883cbbdfc5a518eb96918","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:23:39.051Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"caaee647ec6883cbbdfc5a518eb96918","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:24:06.370Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f16801865a3b08911d27828d3ccb3f4d","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:06.372Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"f16801865a3b08911d27828d3ccb3f4d","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:06.374Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f16801865a3b08911d27828d3ccb3f4d","docName":"simple-test-doc","duration":4,"totalConnections":1}}
{"timestamp":"2025-07-04T21:24:06.382Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ba19a23f7bcea945bebe4c0b78c4abe3","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:06.383Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ba19a23f7bcea945bebe4c0b78c4abe3","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:09.197Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"caaee647ec6883cbbdfc5a518eb96918","docName":"simple-test-doc","duration":30146,"totalConnections":1}}
{"timestamp":"2025-07-04T21:24:09.549Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f74d2f68de06149f62aef01313616b26","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:09.552Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"f74d2f68de06149f62aef01313616b26","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:39.373Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ba19a23f7bcea945bebe4c0b78c4abe3","docName":"simple-test-doc","duration":32991,"totalConnections":1}}
{"timestamp":"2025-07-04T21:24:39.475Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0305f3abd72759b520b0f05e11084a9c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:39.475Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0305f3abd72759b520b0f05e11084a9c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:42.218Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f74d2f68de06149f62aef01313616b26","docName":"simple-test-doc","duration":32669,"totalConnections":1}}
{"timestamp":"2025-07-04T21:24:42.337Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0aa6b6b47d7005a067781a8b7486e575","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:24:42.338Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0aa6b6b47d7005a067781a8b7486e575","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:12.392Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0305f3abd72759b520b0f05e11084a9c","docName":"simple-test-doc","duration":32918,"totalConnections":1}}
{"timestamp":"2025-07-04T21:25:12.475Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"42caec9a4d90b94498237b7bb91e40b1","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:12.476Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"42caec9a4d90b94498237b7bb91e40b1","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:15.197Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0aa6b6b47d7005a067781a8b7486e575","docName":"simple-test-doc","duration":32860,"totalConnections":1}}
{"timestamp":"2025-07-04T21:25:15.326Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0f550973d63112b294ed2e7a8d9a689c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:15.327Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0f550973d63112b294ed2e7a8d9a689c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:45.382Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"42caec9a4d90b94498237b7bb91e40b1","docName":"simple-test-doc","duration":32905,"totalConnections":1}}
{"timestamp":"2025-07-04T21:25:45.492Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c969c345b59a53b1a963e46a998628ea","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:45.494Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c969c345b59a53b1a963e46a998628ea","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:48.195Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0f550973d63112b294ed2e7a8d9a689c","docName":"simple-test-doc","duration":32869,"totalConnections":1}}
{"timestamp":"2025-07-04T21:25:48.307Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e58aff9b96cdb0c31419d3911731556c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:25:48.308Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e58aff9b96cdb0c31419d3911731556c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:26:18.381Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c969c345b59a53b1a963e46a998628ea","docName":"simple-test-doc","duration":32888,"totalConnections":1}}
{"timestamp":"2025-07-04T21:26:18.483Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"972d5563de2b32247af613e5cd7390a4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:26:18.484Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"972d5563de2b32247af613e5cd7390a4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:26:21.197Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e58aff9b96cdb0c31419d3911731556c","docName":"simple-test-doc","duration":32890,"totalConnections":1}}
{"timestamp":"2025-07-04T21:26:21.310Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4a9f568e002ea4f74abb925c11a29f0c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:26:21.311Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4a9f568e002ea4f74abb925c11a29f0c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:26:57.802Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:26:57.813Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:26:57.815Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:26:57.824Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":31323}}
{"timestamp":"2025-07-04T21:26:59.690Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"simple-test-doc","totalDocuments":1}}
{"timestamp":"2025-07-04T21:26:59.691Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"afcca30123bed888914a4a4ae8c42e94","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:26:59.694Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"afcca30123bed888914a4a4ae8c42e94","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:26:59.699Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"11b8bb59b2018a03a39fc4bad896caf4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:26:59.700Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"11b8bb59b2018a03a39fc4bad896caf4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:27:19.728Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"13df1f0f802573d524764b2d2de80ca8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:19.733Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"13df1f0f802573d524764b2d2de80ca8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:19.757Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"13df1f0f802573d524764b2d2de80ca8","docName":"simple-test-doc","duration":29,"totalConnections":2}}
{"timestamp":"2025-07-04T21:27:19.761Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"fce88a9f5374b1a313730344cfbc68f4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:19.759Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fce88a9f5374b1a313730344cfbc68f4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:30.195Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"afcca30123bed888914a4a4ae8c42e94","docName":"simple-test-doc","duration":30503,"totalConnections":2}}
{"timestamp":"2025-07-04T21:27:30.302Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fdaf7d2e4a4c4c77b542211f7e546ffc","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:30.303Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"fdaf7d2e4a4c4c77b542211f7e546ffc","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:31.364Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"11b8bb59b2018a03a39fc4bad896caf4","docName":"simple-test-doc","duration":31664,"totalConnections":2}}
{"timestamp":"2025-07-04T21:27:32.365Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d98fc6ddcf0b3df8e3f13f71f9026dd8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:32.366Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d98fc6ddcf0b3df8e3f13f71f9026dd8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:52.714Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fce88a9f5374b1a313730344cfbc68f4","docName":"simple-test-doc","duration":32955,"totalConnections":2}}
{"timestamp":"2025-07-04T21:27:52.822Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6335d71b6a822614082a0c5dbfe4e65d","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:27:52.827Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6335d71b6a822614082a0c5dbfe4e65d","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:03.200Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fdaf7d2e4a4c4c77b542211f7e546ffc","docName":"simple-test-doc","duration":32898,"totalConnections":2}}
{"timestamp":"2025-07-04T21:28:03.308Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"33797975db86e5e63307b62db0d62732","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:03.309Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"33797975db86e5e63307b62db0d62732","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:04.362Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d98fc6ddcf0b3df8e3f13f71f9026dd8","docName":"simple-test-doc","duration":31997,"totalConnections":2}}
{"timestamp":"2025-07-04T21:28:05.376Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31af31fb7ea0906fe99058b97a96157e","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:05.378Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"31af31fb7ea0906fe99058b97a96157e","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:25.710Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6335d71b6a822614082a0c5dbfe4e65d","docName":"simple-test-doc","duration":32888,"totalConnections":2}}
{"timestamp":"2025-07-04T21:28:25.817Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a76524597331588dcd64440f233ebc50","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:25.818Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"a76524597331588dcd64440f233ebc50","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:36.196Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"33797975db86e5e63307b62db0d62732","docName":"simple-test-doc","duration":32888,"totalConnections":2}}
{"timestamp":"2025-07-04T21:28:36.309Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ac95729d1c1d45bd8ddcb479e87013fa","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:36.310Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ac95729d1c1d45bd8ddcb479e87013fa","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:28:42.404Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31af31fb7ea0906fe99058b97a96157e","docName":"simple-test-doc","duration":37028,"totalConnections":2}}
{"timestamp":"2025-07-04T21:28:58.704Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a76524597331588dcd64440f233ebc50","docName":"simple-test-doc","duration":32887,"totalConnections":1}}
{"timestamp":"2025-07-04T21:28:58.810Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:28:58.810Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:29:09.205Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ac95729d1c1d45bd8ddcb479e87013fa","docName":"simple-test-doc","duration":32896,"totalConnections":1}}
{"timestamp":"2025-07-04T21:29:09.303Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0f5405caa50052781480c7394d5c922c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:29:09.304Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0f5405caa50052781480c7394d5c922c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:29:31.713Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","duration":32902,"totalConnections":1}}
{"timestamp":"2025-07-04T21:29:31.811Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"62cc9857129283129710651c3f0437d7","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:29:31.812Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"62cc9857129283129710651c3f0437d7","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:29:42.196Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0f5405caa50052781480c7394d5c922c","docName":"simple-test-doc","duration":32893,"totalConnections":1}}
{"timestamp":"2025-07-04T21:29:42.308Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"55dd6e223eb700fc19cc4d05e98557de","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:29:42.309Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"55dd6e223eb700fc19cc4d05e98557de","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:29:42.455Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1ae3302b33c2aff1637c258e2296cdfa","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:29:42.456Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1ae3302b33c2aff1637c258e2296cdfa","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:30:10.399Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:30:10.410Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:30:10.412Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:30:10.421Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":36049}}
{"timestamp":"2025-07-04T21:30:10.615Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"simple-test-doc","totalDocuments":1}}
{"timestamp":"2025-07-04T21:30:10.616Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6f5d20114a99df0860f4ae569bfd612f","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:30:10.617Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6f5d20114a99df0860f4ae569bfd612f","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:30:12.147Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3db05b0d6ce24a8b84c726794a22558c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:30:12.149Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"3db05b0d6ce24a8b84c726794a22558c","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:30:12.366Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"8fb29f34720e7eddd6c136d94cee4819","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:30:12.369Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"8fb29f34720e7eddd6c136d94cee4819","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:30:32.887Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ed58e4cbe485d662c4ccb4b6a01e0584","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:30:32.890Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ed58e4cbe485d662c4ccb4b6a01e0584","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:30:32.918Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fb98197180296e1c32450a47c629f35e","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:30:32.920Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"fb98197180296e1c32450a47c629f35e","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:30:32.922Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ed58e4cbe485d662c4ccb4b6a01e0584","docName":"simple-test-doc","duration":35,"totalConnections":4}}
{"timestamp":"2025-07-04T21:30:40.717Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6f5d20114a99df0860f4ae569bfd612f","docName":"simple-test-doc","duration":30100,"totalConnections":3}}
{"timestamp":"2025-07-04T21:30:40.811Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"56f7c45771e55031ef6fd87528689fbe","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:30:40.812Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"56f7c45771e55031ef6fd87528689fbe","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:30:42.375Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3db05b0d6ce24a8b84c726794a22558c","docName":"simple-test-doc","duration":30228,"totalConnections":3}}
{"timestamp":"2025-07-04T21:30:43.394Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0b6f9187fce3c6c4c9ebf781ade82b23","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:30:43.395Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0b6f9187fce3c6c4c9ebf781ade82b23","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:30:56.103Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7cbb6f5bb6aa5bbe1ce1df689e454a96","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:30:56.106Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"7cbb6f5bb6aa5bbe1ce1df689e454a96","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:30:56.107Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"56f7c45771e55031ef6fd87528689fbe","docName":"simple-test-doc","duration":15296,"totalConnections":4}}
{"timestamp":"2025-07-04T21:30:56.121Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ca88f2ab890c91f0e66a73d396b29e01","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:30:56.120Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ca88f2ab890c91f0e66a73d396b29e01","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:30:56.122Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fb98197180296e1c32450a47c629f35e","docName":"simple-test-doc","duration":23204,"totalConnections":4}}
{"timestamp":"2025-07-04T21:30:56.385Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0b6f9187fce3c6c4c9ebf781ade82b23","docName":"simple-test-doc","duration":12991,"totalConnections":3}}
{"timestamp":"2025-07-04T21:30:56.389Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"8fb29f34720e7eddd6c136d94cee4819","docName":"simple-test-doc","duration":44023,"totalConnections":2}}
{"timestamp":"2025-07-04T21:30:56.394Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:30:56.394Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:30:56.396Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c7539a8950cd90c28e380ed5c6fc9cdb","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:30:56.396Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c7539a8950cd90c28e380ed5c6fc9cdb","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:31:21.740Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d62de48aa1bfd1b7c8a8842af2f042e1","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:21.750Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d62de48aa1bfd1b7c8a8842af2f042e1","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:21.751Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d62de48aa1bfd1b7c8a8842af2f042e1","docName":"simple-test-doc","duration":11,"totalConnections":4}}
{"timestamp":"2025-07-04T21:31:21.752Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"59dfe6d38293b70b3f6bdb0f60c9c21b","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:21.754Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"59dfe6d38293b70b3f6bdb0f60c9c21b","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:26.362Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7cbb6f5bb6aa5bbe1ce1df689e454a96","docName":"simple-test-doc","duration":30260,"totalConnections":4}}
{"timestamp":"2025-07-04T21:31:27.365Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c7539a8950cd90c28e380ed5c6fc9cdb","docName":"simple-test-doc","duration":30969,"totalConnections":3}}
{"timestamp":"2025-07-04T21:31:27.367Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"11c02b2aa01f3f263cb71d90ce18b179","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:31:27.368Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"11c02b2aa01f3f263cb71d90ce18b179","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:31:28.370Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b19da85e8fec5a20487f69dbdbd10c98","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:28.370Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"b19da85e8fec5a20487f69dbdbd10c98","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:29.079Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ca88f2ab890c91f0e66a73d396b29e01","docName":"simple-test-doc","duration":32959,"totalConnections":4}}
{"timestamp":"2025-07-04T21:31:29.185Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6e2d299aae55bbd7d8296b933d0fb2be","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:29.186Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6e2d299aae55bbd7d8296b933d0fb2be","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:31:42.376Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","duration":45982,"totalConnections":4}}
{"timestamp":"2025-07-04T21:31:54.728Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"59dfe6d38293b70b3f6bdb0f60c9c21b","docName":"simple-test-doc","duration":32976,"totalConnections":3}}
{"timestamp":"2025-07-04T21:31:54.831Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4ff10f7fc4e8e38436d2d510c0f1b9c5","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:31:54.831Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4ff10f7fc4e8e38436d2d510c0f1b9c5","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:31:59.363Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"11c02b2aa01f3f263cb71d90ce18b179","docName":"simple-test-doc","duration":31996,"totalConnections":3}}
{"timestamp":"2025-07-04T21:32:00.368Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"db0155497f5405a01c09408658130076","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:00.370Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"db0155497f5405a01c09408658130076","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:02.090Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6e2d299aae55bbd7d8296b933d0fb2be","docName":"simple-test-doc","duration":32905,"totalConnections":3}}
{"timestamp":"2025-07-04T21:32:02.190Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0536abfbb42ee0b7ebfe524d95202348","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:02.192Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0536abfbb42ee0b7ebfe524d95202348","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:27.729Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4ff10f7fc4e8e38436d2d510c0f1b9c5","docName":"simple-test-doc","duration":32898,"totalConnections":3}}
{"timestamp":"2025-07-04T21:32:27.836Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6193dc79fc1f5faadcadaa03fff64579","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:27.836Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6193dc79fc1f5faadcadaa03fff64579","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:35.080Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0536abfbb42ee0b7ebfe524d95202348","docName":"simple-test-doc","duration":32890,"totalConnections":3}}
{"timestamp":"2025-07-04T21:32:35.198Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b2b4239fcebba714d57c337262b610cb","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:35.200Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"b2b4239fcebba714d57c337262b610cb","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:42.372Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b19da85e8fec5a20487f69dbdbd10c98","docName":"simple-test-doc","duration":74002,"totalConnections":3}}
{"timestamp":"2025-07-04T21:32:42.376Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"83b303afe21b691cbfad75c1254bb906","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:42.379Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"83b303afe21b691cbfad75c1254bb906","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:32:42.380Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"db0155497f5405a01c09408658130076","docName":"simple-test-doc","duration":42012,"totalConnections":3}}
{"timestamp":"2025-07-04T21:33:00.734Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6193dc79fc1f5faadcadaa03fff64579","docName":"simple-test-doc","duration":32898,"totalConnections":2}}
{"timestamp":"2025-07-04T21:33:00.836Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"85bea8416e89371926135d49edd373fb","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:00.837Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"85bea8416e89371926135d49edd373fb","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:08.081Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b2b4239fcebba714d57c337262b610cb","docName":"simple-test-doc","duration":32883,"totalConnections":2}}
{"timestamp":"2025-07-04T21:33:08.187Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"547ff59a79939812eb4bd41364bce7c8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:08.188Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"547ff59a79939812eb4bd41364bce7c8","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:33.737Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"85bea8416e89371926135d49edd373fb","docName":"simple-test-doc","duration":32900,"totalConnections":2}}
{"timestamp":"2025-07-04T21:33:33.838Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"906f426c871a8f6cc255e64cdb8e39ac","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:33.838Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"906f426c871a8f6cc255e64cdb8e39ac","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:41.087Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"547ff59a79939812eb4bd41364bce7c8","docName":"simple-test-doc","duration":32900,"totalConnections":2}}
{"timestamp":"2025-07-04T21:33:41.193Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6e7971965b2be9cf5e76b068acf855ed","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:41.194Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6e7971965b2be9cf5e76b068acf855ed","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:42.402Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"83b303afe21b691cbfad75c1254bb906","docName":"simple-test-doc","duration":60026,"totalConnections":2}}
{"timestamp":"2025-07-04T21:33:42.403Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"879a35c1fdea53b1602254bb5c237dec","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:42.404Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"879a35c1fdea53b1602254bb5c237dec","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:33:42.486Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"2271cdf4f2008163d3929c8ca5ee43db","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:33:42.488Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"2271cdf4f2008163d3929c8ca5ee43db","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:34:06.737Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"906f426c871a8f6cc255e64cdb8e39ac","docName":"simple-test-doc","duration":32899,"totalConnections":3}}
{"timestamp":"2025-07-04T21:34:06.840Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4ef027b71d2acd98357b4de5389149fa","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:34:06.841Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4ef027b71d2acd98357b4de5389149fa","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:34:27.124Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:34:27.134Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:34:27.138Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:34:27.145Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":42423}}
{"timestamp":"2025-07-04T21:34:27.256Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"simple-test-doc","totalDocuments":1}}
{"timestamp":"2025-07-04T21:34:27.257Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0a2b7f01bc7a57b453774cf9e292639e","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:34:27.258Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0a2b7f01bc7a57b453774cf9e292639e","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:34:27.261Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"be9474c3b3ede87689375527f2375456","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:34:27.261Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"be9474c3b3ede87689375527f2375456","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:34:27.380Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f4f46ee57fe6ca8da44a186904a7f087","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:34:27.381Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"f4f46ee57fe6ca8da44a186904a7f087","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:34:27.387Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1e6551b8ba2d441b4dfc7ae423060702","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:34:27.388Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1e6551b8ba2d441b4dfc7ae423060702","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:34:42.395Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"15cb0defb07522a4fc4a9c390ce9cebe","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:34:42.397Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"15cb0defb07522a4fc4a9c390ce9cebe","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:34:49.658Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"711bd91e8229ebc0aa13c3f48dee24f0","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:34:49.659Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"711bd91e8229ebc0aa13c3f48dee24f0","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:34:49.661Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"711bd91e8229ebc0aa13c3f48dee24f0","docName":"simple-test-doc","duration":2,"totalConnections":5}}
{"timestamp":"2025-07-04T21:34:49.674Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"49c2f74ddd2726f957c0590eb4c9b957","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:34:49.684Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"49c2f74ddd2726f957c0590eb4c9b957","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:34:58.374Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"be9474c3b3ede87689375527f2375456","docName":"simple-test-doc","duration":31113,"totalConnections":5}}
{"timestamp":"2025-07-04T21:34:59.089Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0a2b7f01bc7a57b453774cf9e292639e","docName":"simple-test-doc","duration":31832,"totalConnections":4}}
{"timestamp":"2025-07-04T21:34:59.195Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"8645da1c73a04c94e6e777bdea921cf9","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:34:59.197Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"8645da1c73a04c94e6e777bdea921cf9","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:34:59.377Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d10708026447227de988223ee3759a65","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:34:59.378Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d10708026447227de988223ee3759a65","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:35:22.655Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"49c2f74ddd2726f957c0590eb4c9b957","docName":"simple-test-doc","duration":32981,"totalConnections":5}}
{"timestamp":"2025-07-04T21:35:22.754Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9*******************************","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:35:22.754Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9*******************************","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:35:31.365Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d10708026447227de988223ee3759a65","docName":"simple-test-doc","duration":31988,"totalConnections":5}}
{"timestamp":"2025-07-04T21:35:32.082Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"8645da1c73a04c94e6e777bdea921cf9","docName":"simple-test-doc","duration":32887,"totalConnections":4}}
{"timestamp":"2025-07-04T21:35:32.191Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"19f0eaf164b31f30853dde27ae17ccff","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:35:32.192Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"19f0eaf164b31f30853dde27ae17ccff","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:35:32.380Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"65f80d67bd2868376a1e89e7c46d17c9","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:35:32.381Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"65f80d67bd2868376a1e89e7c46d17c9","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:35:42.375Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"15cb0defb07522a4fc4a9c390ce9cebe","docName":"simple-test-doc","duration":59981,"totalConnections":5}}
{"timestamp":"2025-07-04T21:35:42.378Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f4f46ee57fe6ca8da44a186904a7f087","docName":"simple-test-doc","duration":74998,"totalConnections":4}}
{"timestamp":"2025-07-04T21:35:42.389Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1e6551b8ba2d441b4dfc7ae423060702","docName":"simple-test-doc","duration":75002,"totalConnections":3}}
{"timestamp":"2025-07-04T21:35:55.648Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9*******************************","docName":"simple-test-doc","duration":32894,"totalConnections":2}}
{"timestamp":"2025-07-04T21:35:55.755Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bb628b8a272822f2a34820fc521f654f","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:35:55.756Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"bb628b8a272822f2a34820fc521f654f","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:36:05.085Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"19f0eaf164b31f30853dde27ae17ccff","docName":"simple-test-doc","duration":32893,"totalConnections":2}}
{"timestamp":"2025-07-04T21:36:05.225Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4b185516426ecd0a02f1ad4303a3546c","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:36:05.225Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4b185516426ecd0a02f1ad4303a3546c","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:36:28.831Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a049a6cb4bec60faf07b8349a4b22719","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:28.833Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"a049a6cb4bec60faf07b8349a4b22719","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:28.834Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a049a6cb4bec60faf07b8349a4b22719","docName":"simple-test-doc","duration":3,"totalConnections":3}}
{"timestamp":"2025-07-04T21:36:28.835Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"88fb9172dae4e8f8da87ff607cf930bf","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:28.834Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"88fb9172dae4e8f8da87ff607cf930bf","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:29.366Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bb628b8a272822f2a34820fc521f654f","docName":"simple-test-doc","duration":33611,"totalConnections":3}}
{"timestamp":"2025-07-04T21:36:30.376Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fc9b29c008a687b40b46cc77a796f029","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:30.377Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"fc9b29c008a687b40b46cc77a796f029","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:38.083Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4b185516426ecd0a02f1ad4303a3546c","docName":"simple-test-doc","duration":32858,"totalConnections":3}}
{"timestamp":"2025-07-04T21:36:38.214Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ed72f10984f0c12c0aff3c50b1daed02","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:38.218Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ed72f10984f0c12c0aff3c50b1daed02","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:36:42.414Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"620e30f84946cee7280a41865968494c","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:36:42.417Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"620e30f84946cee7280a41865968494c","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:36:42.418Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"65f80d67bd2868376a1e89e7c46d17c9","docName":"simple-test-doc","duration":70038,"totalConnections":4}}
{"timestamp":"2025-07-04T21:36:42.423Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"de224c70a518dd9f8798b923942f31c4","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:36:42.427Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"de224c70a518dd9f8798b923942f31c4","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:36:42.433Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ba6a9e4b4a387bb358a2c89bd25b965c","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:36:42.435Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ba6a9e4b4a387bb358a2c89bd25b965c","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:01.815Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"88fb9172dae4e8f8da87ff607cf930bf","docName":"simple-test-doc","duration":32980,"totalConnections":5}}
{"timestamp":"2025-07-04T21:37:01.916Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a831f7e4377045489a3c1ce0a786f67b","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:01.916Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"a831f7e4377045489a3c1ce0a786f67b","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:02.367Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"fc9b29c008a687b40b46cc77a796f029","docName":"simple-test-doc","duration":31990,"totalConnections":5}}
{"timestamp":"2025-07-04T21:37:03.377Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5e879e96ed74677619a0a8626293bec4","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:03.378Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"5e879e96ed74677619a0a8626293bec4","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:11.083Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ed72f10984f0c12c0aff3c50b1daed02","docName":"simple-test-doc","duration":32869,"totalConnections":5}}
{"timestamp":"2025-07-04T21:37:11.194Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"24b8fb92beb7839cdda1cec980bb85e2","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:11.194Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"24b8fb92beb7839cdda1cec980bb85e2","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:34.818Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a831f7e4377045489a3c1ce0a786f67b","docName":"simple-test-doc","duration":32902,"totalConnections":5}}
{"timestamp":"2025-07-04T21:37:34.919Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:34.919Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"********************************","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:37:42.418Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5e879e96ed74677619a0a8626293bec4","docName":"simple-test-doc","duration":39041,"totalConnections":5}}
{"timestamp":"2025-07-04T21:37:42.419Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ba6a9e4b4a387bb358a2c89bd25b965c","docName":"simple-test-doc","duration":59986,"totalConnections":4}}
{"timestamp":"2025-07-04T21:37:42.424Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"de224c70a518dd9f8798b923942f31c4","docName":"simple-test-doc","duration":60001,"totalConnections":3}}
{"timestamp":"2025-07-04T21:37:42.425Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"620e30f84946cee7280a41865968494c","docName":"simple-test-doc","duration":60011,"totalConnections":2}}
{"timestamp":"2025-07-04T21:37:42.428Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"79da2b5656e00eb21a864ea03b2b7f59","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:37:42.426Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"79da2b5656e00eb21a864ea03b2b7f59","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:37:44.098Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"24b8fb92beb7839cdda1cec980bb85e2","docName":"simple-test-doc","duration":32904,"totalConnections":2}}
{"timestamp":"2025-07-04T21:37:44.200Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"54d05fc3b72d4ce83f38419fd53539f1","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:37:44.201Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"54d05fc3b72d4ce83f38419fd53539f1","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:38:07.812Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"********************************","docName":"simple-test-doc","duration":32893,"totalConnections":2}}
{"timestamp":"2025-07-04T21:38:07.922Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"830173c6fd9de8502ccb24031ace95f5","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:38:07.923Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"830173c6fd9de8502ccb24031ace95f5","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:38:17.354Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ef18c3be73be2e50156a94a92a046f67","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:38:17.391Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ef18c3be73be2e50156a94a92a046f67","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:38:17.393Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"54d05fc3b72d4ce83f38419fd53539f1","docName":"simple-test-doc","duration":33193,"totalConnections":3}}
{"timestamp":"2025-07-04T21:38:38.367Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"830173c6fd9de8502ccb24031ace95f5","docName":"simple-test-doc","duration":30445,"totalConnections":2}}
{"timestamp":"2025-07-04T21:38:39.378Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5de674972bc70025aad95ad5aa64c836","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:38:39.379Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"5de674972bc70025aad95ad5aa64c836","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:38:42.385Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"79da2b5656e00eb21a864ea03b2b7f59","docName":"simple-test-doc","duration":59959,"totalConnections":2}}
{"timestamp":"2025-07-04T21:38:42.434Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"8446a62893df77b5df90a37c3c742092","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:38:42.435Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"8446a62893df77b5df90a37c3c742092","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:38:42.456Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bb977b699e5e75f68e78eed975f97c5e","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:38:42.456Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"bb977b699e5e75f68e78eed975f97c5e","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:38:42.461Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"eb6d9f6c7f42caf9f379e5dd52fdc1a5","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:38:42.461Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"eb6d9f6c7f42caf9f379e5dd52fdc1a5","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:38:42.480Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"797a3f1954355d742008bbc3484cfac5","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:38:42.482Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"797a3f1954355d742008bbc3484cfac5","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:38:50.086Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ef18c3be73be2e50156a94a92a046f67","docName":"simple-test-doc","duration":32732,"totalConnections":5}}
{"timestamp":"2025-07-04T21:38:50.196Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"42aa9573b9a06bc7f43c9e93099d4312","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:38:50.197Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"42aa9573b9a06bc7f43c9e93099d4312","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:39:11.368Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5de674972bc70025aad95ad5aa64c836","docName":"simple-test-doc","duration":31989,"totalConnections":5}}
{"timestamp":"2025-07-04T21:39:12.375Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"cc5cb7434e2f85e5ed3e7129f81a14e1","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:39:12.376Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"cc5cb7434e2f85e5ed3e7129f81a14e1","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:43:28.691Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:43:28.708Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:43:28.709Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:43:28.720Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":56005}}
{"timestamp":"2025-07-04T21:43:28.888Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:28.888Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:29.901Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:29.902Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:30.574Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:30.575Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:30.703Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:30.704Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:30.717Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:30.717Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:30.790Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:30.790Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:30.879Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:30.879Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:31.055Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:31.055Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:32.925Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:32.926Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:33.110Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:33.110Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:33.255Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:33.255Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:33.270Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:33.271Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:33.432Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:33.432Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:33.861Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:33.861Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:34.155Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:34.156Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:34.666Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:34.666Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:35.980Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:35.980Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:36.365Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:36.366Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:36.550Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:36.551Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:36.565Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:36.565Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:36.621Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:36.621Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:36.812Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:36.812Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:38.074Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:38.074Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:38.530Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:38.530Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:39.721Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:39.722Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:40.945Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:40.945Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:41.083Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:41.083Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:41.180Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:41.181Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:42.347Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:42.348Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:42.383Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:42.383Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:42.774Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:42.774Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:44.068Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:44.069Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:44.139Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:44.140Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:44.362Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:44.362Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:45.249Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:45.249Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:45.741Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:45.741Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:45.881Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:45.881Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:46.721Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:46.721Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:46.939Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:46.939Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:47.875Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:47.876Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:48.179Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:48.179Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:48.398Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:48.398Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:48.849Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:48.849Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:48.990Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:48.990Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:49.806Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:49.807Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:49.820Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:49.820Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:49.996Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:49.996Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:50.230Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:50.231Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:51.067Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:51.067Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:51.588Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:51.588Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:51.673Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:51.673Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:52.308Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:52.308Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:53.916Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:53.916Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:54.426Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:54.426Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:54.631Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:54.631Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:54.715Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:54.715Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:54.814Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:54.814Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:55.239Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:55.240Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:55.321Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:55.321Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:56.972Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:56.973Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:57.450Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:57.450Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:58.363Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:58.363Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:59.309Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:59.309Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:43:59.992Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:43:59.992Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:00.102Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:00.103Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:00.432Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:00.433Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:00.499Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:00.499Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:01.321Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:01.321Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:01.660Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:01.660Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:01.872Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:01.872Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:02.972Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:02.973Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:03.699Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:03.700Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:04.004Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:04.004Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:04.281Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:04.282Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:04.439Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:04.439Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:04.781Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:04.782Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:04.938Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:04.938Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:06.583Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:06.584Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:06.765Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:06.766Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:07.069Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:07.070Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:07.616Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:07.617Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:07.634Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:07.634Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:08.560Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:08.560Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:09.949Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:09.950Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:09.965Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:09.965Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:10.462Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:10.463Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:10.490Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:10.490Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:10.605Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:10.606Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:11.387Z","level":"warn","message":"Origin not allowed","service":"y-websocket-server","component":"SecurityMiddleware","metadata":{"origin":"http://localhost:3001","allowedOrigins":["http://localhost:3000"]}}
{"timestamp":"2025-07-04T21:44:11.388Z","level":"warn","message":"WebSocket upgrade validation failed","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"reason":"Origin not allowed","origin":"http://localhost:3001"}}
{"timestamp":"2025-07-04T21:44:22.868Z","level":"info","message":"Initializing Y.js WebSocket Server","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"nodeEnv":"development","host":"localhost","port":1234}}
{"timestamp":"2025-07-04T21:44:22.877Z","level":"info","message":"LevelDB persistence initialized","service":"y-websocket-server","component":"LevelDBPersistence","metadata":{"directory":"./data"}}
{"timestamp":"2025-07-04T21:44:22.880Z","level":"info","message":"Y.js WebSocket Server initialized successfully","service":"y-websocket-server","component":"YWebSocketServer","metadata":{}}
{"timestamp":"2025-07-04T21:44:22.886Z","level":"info","message":"Y.js WebSocket Server started","service":"y-websocket-server","component":"YWebSocketServer","metadata":{"host":"localhost","port":1234,"pid":57369}}
{"timestamp":"2025-07-04T21:44:23.132Z","level":"info","message":"Document created","service":"y-websocket-server","component":"DocumentManager","metadata":{"docName":"simple-test-doc","totalDocuments":1}}
{"timestamp":"2025-07-04T21:44:23.133Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1a59a1ffd4479afea13131a0a93c87db","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:44:23.135Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1a59a1ffd4479afea13131a0a93c87db","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:44:23.739Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d16940b08a2723c75a5c23a120247fd4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:44:23.740Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d16940b08a2723c75a5c23a120247fd4","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:44:25.132Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0824b3a1482062e40361f2c5dd1dcf29","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:44:25.133Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0824b3a1482062e40361f2c5dd1dcf29","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:44:25.518Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"dfe40b66441885f1397af7bba8f1052d","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:44:25.519Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"dfe40b66441885f1397af7bba8f1052d","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:44:25.733Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5596110330e463ce32c55517afc3752c","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:44:25.734Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"5596110330e463ce32c55517afc3752c","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:44:26.073Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5ea5d7b18e1827fb29abe236652ec5af","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:44:26.075Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"5ea5d7b18e1827fb29abe236652ec5af","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:44:27.581Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9640fe4df468b9ee0ec462bedefd1eba","docName":"simple-test-doc","totalConnections":7}}
{"timestamp":"2025-07-04T21:44:27.584Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9640fe4df468b9ee0ec462bedefd1eba","docName":"simple-test-doc","totalConnections":7}}
{"timestamp":"2025-07-04T21:44:28.366Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e681e59eeeea5f8817ccc181cb5e2c23","docName":"simple-test-doc","totalConnections":8}}
{"timestamp":"2025-07-04T21:44:28.367Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e681e59eeeea5f8817ccc181cb5e2c23","docName":"simple-test-doc","totalConnections":8}}
{"timestamp":"2025-07-04T21:44:31.252Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d16940b08a2723c75a5c23a120247fd4","docName":"simple-test-doc","duration":7512,"totalConnections":7}}
{"timestamp":"2025-07-04T21:44:31.976Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"14301f652d70b41219fcbd76d3aa9258","docName":"simple-test-doc","totalConnections":8}}
{"timestamp":"2025-07-04T21:44:31.979Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"14301f652d70b41219fcbd76d3aa9258","docName":"simple-test-doc","totalConnections":8}}
{"timestamp":"2025-07-04T21:44:48.558Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"68a3d1f89b81d5764f122378335f3fc8","docName":"simple-test-doc","totalConnections":9}}
{"timestamp":"2025-07-04T21:44:48.560Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"68a3d1f89b81d5764f122378335f3fc8","docName":"simple-test-doc","totalConnections":9}}
{"timestamp":"2025-07-04T21:44:48.566Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"68a3d1f89b81d5764f122378335f3fc8","docName":"simple-test-doc","duration":8,"totalConnections":8}}
{"timestamp":"2025-07-04T21:44:48.572Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1c4b98f2b07c6a49c03c6e62a03c766f","docName":"simple-test-doc","totalConnections":9}}
{"timestamp":"2025-07-04T21:44:48.573Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1c4b98f2b07c6a49c03c6e62a03c766f","docName":"simple-test-doc","totalConnections":9}}
{"timestamp":"2025-07-04T21:44:51.755Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0824b3a1482062e40361f2c5dd1dcf29","docName":"simple-test-doc","duration":26623,"totalConnections":8}}
{"timestamp":"2025-07-04T21:44:52.185Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"14301f652d70b41219fcbd76d3aa9258","docName":"simple-test-doc","duration":20209,"totalConnections":7}}
{"timestamp":"2025-07-04T21:44:52.676Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1c4b98f2b07c6a49c03c6e62a03c766f","docName":"simple-test-doc","duration":4104,"totalConnections":6}}
{"timestamp":"2025-07-04T21:44:56.345Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5596110330e463ce32c55517afc3752c","docName":"simple-test-doc","duration":30612,"totalConnections":5}}
{"timestamp":"2025-07-04T21:45:00.042Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d315fcad2e7a5f7c7d2fae3e73a6e38c","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:45:00.043Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d315fcad2e7a5f7c7d2fae3e73a6e38c","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:45:32.347Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d315fcad2e7a5f7c7d2fae3e73a6e38c","docName":"simple-test-doc","duration":32305,"totalConnections":5}}
{"timestamp":"2025-07-04T21:45:32.503Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c8cd75bb7a1550b4f10740f0ddc9c24b","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:45:32.503Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c8cd75bb7a1550b4f10740f0ddc9c24b","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:45:42.374Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1a59a1ffd4479afea13131a0a93c87db","docName":"simple-test-doc","duration":79241,"totalConnections":5}}
{"timestamp":"2025-07-04T21:45:42.376Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e681e59eeeea5f8817ccc181cb5e2c23","docName":"simple-test-doc","duration":74010,"totalConnections":4}}
{"timestamp":"2025-07-04T21:45:42.378Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"dfe40b66441885f1397af7bba8f1052d","docName":"simple-test-doc","duration":76860,"totalConnections":3}}
{"timestamp":"2025-07-04T21:45:42.384Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9640fe4df468b9ee0ec462bedefd1eba","docName":"simple-test-doc","duration":74803,"totalConnections":2}}
{"timestamp":"2025-07-04T21:45:42.385Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5ea5d7b18e1827fb29abe236652ec5af","docName":"simple-test-doc","duration":76312,"totalConnections":1}}
{"timestamp":"2025-07-04T21:46:05.349Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c8cd75bb7a1550b4f10740f0ddc9c24b","docName":"simple-test-doc","duration":32844,"totalConnections":0}}
{"timestamp":"2025-07-04T21:46:05.468Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31375bcbb9f1d651c6ec9cb053fab888","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:46:05.468Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"31375bcbb9f1d651c6ec9cb053fab888","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:46:38.344Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31375bcbb9f1d651c6ec9cb053fab888","docName":"simple-test-doc","duration":32876,"totalConnections":0}}
{"timestamp":"2025-07-04T21:46:38.450Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0d672bb3ecd216e8e3cefdbc9e5c73ae","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:46:38.450Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"0d672bb3ecd216e8e3cefdbc9e5c73ae","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:46:42.378Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e89f95ea87ae14aa4b8a053ac9b7cc5d","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:46:42.379Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e89f95ea87ae14aa4b8a053ac9b7cc5d","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:46:42.389Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"28836bc5e47e2e83ea089a3f57384b6b","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:46:42.390Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"28836bc5e47e2e83ea089a3f57384b6b","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:46:42.402Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"84d6df9c90270d4aca7d4de2d6efb89a","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:46:42.403Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"84d6df9c90270d4aca7d4de2d6efb89a","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:46:42.414Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ea208d57d12a2a92f9b4a2cc62927850","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:46:42.417Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"ea208d57d12a2a92f9b4a2cc62927850","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:46:42.431Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b9958f15a16a981f7adb5af8211eb67f","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:46:42.436Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"b9958f15a16a981f7adb5af8211eb67f","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:47:11.343Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"0d672bb3ecd216e8e3cefdbc9e5c73ae","docName":"simple-test-doc","duration":32893,"totalConnections":5}}
{"timestamp":"2025-07-04T21:47:11.449Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1e9f0ef1e783de00b086f541dc695217","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:47:11.450Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"1e9f0ef1e783de00b086f541dc695217","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:47:42.390Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"84d6df9c90270d4aca7d4de2d6efb89a","docName":"simple-test-doc","duration":59988,"totalConnections":5}}
{"timestamp":"2025-07-04T21:47:42.391Z","level":"error","message":"Error sending ping","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"28836bc5e47e2e83ea089a3f57384b6b","error":{}}}
{"timestamp":"2025-07-04T21:47:42.392Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b9958f15a16a981f7adb5af8211eb67f","docName":"simple-test-doc","duration":59961,"totalConnections":4}}
{"timestamp":"2025-07-04T21:47:42.393Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e89f95ea87ae14aa4b8a053ac9b7cc5d","docName":"simple-test-doc","duration":60015,"totalConnections":3}}
{"timestamp":"2025-07-04T21:47:42.393Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"ea208d57d12a2a92f9b4a2cc62927850","docName":"simple-test-doc","duration":59979,"totalConnections":2}}
{"timestamp":"2025-07-04T21:47:42.393Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"28836bc5e47e2e83ea089a3f57384b6b","docName":"simple-test-doc","duration":60004,"totalConnections":1}}
{"timestamp":"2025-07-04T21:47:44.353Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"1e9f0ef1e783de00b086f541dc695217","docName":"simple-test-doc","duration":32904,"totalConnections":0}}
{"timestamp":"2025-07-04T21:47:44.459Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d78c8b36af6aa335807f25ea44911794","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:47:44.459Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d78c8b36af6aa335807f25ea44911794","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:48:17.353Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d78c8b36af6aa335807f25ea44911794","docName":"simple-test-doc","duration":32894,"totalConnections":0}}
{"timestamp":"2025-07-04T21:48:17.451Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c93ee30bdbd01829a14324c162e7c067","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:48:17.451Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c93ee30bdbd01829a14324c162e7c067","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:48:42.379Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"09e1eeea3cf7f66c8282bf4d79feff8a","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:48:42.381Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"09e1eeea3cf7f66c8282bf4d79feff8a","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:48:42.392Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7deabf1602189e59aea9685f489833d4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:48:42.392Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"7deabf1602189e59aea9685f489833d4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:48:42.404Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a4ad38cfccb1c6377e9ff3d00273d8f4","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:48:42.405Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"a4ad38cfccb1c6377e9ff3d00273d8f4","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:48:42.417Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"541b16a22cdde3e94c34cf3f8b94a9a7","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:48:42.418Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"541b16a22cdde3e94c34cf3f8b94a9a7","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:48:42.429Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"00d56461e5e2df7d93f5c05b1738d35a","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:48:42.430Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"00d56461e5e2df7d93f5c05b1738d35a","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:48:50.339Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c93ee30bdbd01829a14324c162e7c067","docName":"simple-test-doc","duration":32888,"totalConnections":5}}
{"timestamp":"2025-07-04T21:48:50.449Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4d5e406cd64376629aef3cbecbff4dae","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:48:50.451Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"4d5e406cd64376629aef3cbecbff4dae","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:49:23.338Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"4d5e406cd64376629aef3cbecbff4dae","docName":"simple-test-doc","duration":32888,"totalConnections":5}}
{"timestamp":"2025-07-04T21:49:23.445Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31bef9d039b495d480d7b5cda2f3de02","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:49:23.446Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"31bef9d039b495d480d7b5cda2f3de02","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:49:42.381Z","level":"error","message":"Error sending ping","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"09e1eeea3cf7f66c8282bf4d79feff8a","error":{}}}
{"timestamp":"2025-07-04T21:49:42.382Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"09e1eeea3cf7f66c8282bf4d79feff8a","docName":"simple-test-doc","duration":60004,"totalConnections":5}}
{"timestamp":"2025-07-04T21:49:42.383Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7deabf1602189e59aea9685f489833d4","docName":"simple-test-doc","duration":59991,"totalConnections":4}}
{"timestamp":"2025-07-04T21:49:42.383Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"a4ad38cfccb1c6377e9ff3d00273d8f4","docName":"simple-test-doc","duration":59979,"totalConnections":3}}
{"timestamp":"2025-07-04T21:49:42.383Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"00d56461e5e2df7d93f5c05b1738d35a","docName":"simple-test-doc","duration":59954,"totalConnections":2}}
{"timestamp":"2025-07-04T21:49:42.383Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"541b16a22cdde3e94c34cf3f8b94a9a7","docName":"simple-test-doc","duration":59966,"totalConnections":1}}
{"timestamp":"2025-07-04T21:49:56.340Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"31bef9d039b495d480d7b5cda2f3de02","docName":"simple-test-doc","duration":32895,"totalConnections":0}}
{"timestamp":"2025-07-04T21:49:56.447Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3910c02950ceb7a3cfa674728e1b1a49","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:49:56.449Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"3910c02950ceb7a3cfa674728e1b1a49","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:50:29.344Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"3910c02950ceb7a3cfa674728e1b1a49","docName":"simple-test-doc","duration":32897,"totalConnections":0}}
{"timestamp":"2025-07-04T21:50:29.446Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f831a659e77b84991939ce986dc95701","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:50:29.446Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"f831a659e77b84991939ce986dc95701","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:50:42.396Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6dd0045fdf5eb5e06b9f015fcfb636fb","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:50:42.399Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6dd0045fdf5eb5e06b9f015fcfb636fb","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:50:42.414Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9b207beb5fd92bc358715a4d9760d52b","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:50:42.415Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9b207beb5fd92bc358715a4d9760d52b","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:50:42.421Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"068c25a278cfdf7b2bb94444fb4f4380","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:50:42.423Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"068c25a278cfdf7b2bb94444fb4f4380","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:50:42.435Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"588fe4405850c44ba7fb47bd454da74f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:50:42.436Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"588fe4405850c44ba7fb47bd454da74f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:50:42.447Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"8615dd6ae86d2e7721c2ecb1ad78647b","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:50:42.448Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"8615dd6ae86d2e7721c2ecb1ad78647b","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:51:02.337Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"f831a659e77b84991939ce986dc95701","docName":"simple-test-doc","duration":32891,"totalConnections":5}}
{"timestamp":"2025-07-04T21:51:02.445Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e490b81e3c4ee66cda2bc50014a8b940","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:51:02.446Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e490b81e3c4ee66cda2bc50014a8b940","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:51:35.337Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e490b81e3c4ee66cda2bc50014a8b940","docName":"simple-test-doc","duration":32892,"totalConnections":5}}
{"timestamp":"2025-07-04T21:51:35.443Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"abbbed8125938bf4df43063864c54829","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:51:35.444Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"abbbed8125938bf4df43063864c54829","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:51:42.368Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"068c25a278cfdf7b2bb94444fb4f4380","docName":"simple-test-doc","duration":59947,"totalConnections":5}}
{"timestamp":"2025-07-04T21:51:42.368Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9b207beb5fd92bc358715a4d9760d52b","docName":"simple-test-doc","duration":59957,"totalConnections":4}}
{"timestamp":"2025-07-04T21:51:42.373Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"588fe4405850c44ba7fb47bd454da74f","docName":"simple-test-doc","duration":59938,"totalConnections":3}}
{"timestamp":"2025-07-04T21:51:42.412Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"8615dd6ae86d2e7721c2ecb1ad78647b","docName":"simple-test-doc","duration":59965,"totalConnections":2}}
{"timestamp":"2025-07-04T21:51:42.420Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6dd0045fdf5eb5e06b9f015fcfb636fb","docName":"simple-test-doc","duration":60025,"totalConnections":1}}
{"timestamp":"2025-07-04T21:52:08.341Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"abbbed8125938bf4df43063864c54829","docName":"simple-test-doc","duration":32897,"totalConnections":0}}
{"timestamp":"2025-07-04T21:52:08.445Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6ed423b65de47a1a594d869f4b84823f","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:52:08.446Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6ed423b65de47a1a594d869f4b84823f","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:52:41.337Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6ed423b65de47a1a594d869f4b84823f","docName":"simple-test-doc","duration":32891,"totalConnections":0}}
{"timestamp":"2025-07-04T21:52:41.442Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"700ac7c15b6b927393d4911ef656e6a7","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:52:41.443Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"700ac7c15b6b927393d4911ef656e6a7","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:52:42.384Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9619c3fc66b19ca57c341260ef4e5eff","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:52:42.385Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9619c3fc66b19ca57c341260ef4e5eff","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:52:42.398Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"62d691c4b51d0658b8f0e5ac2498f107","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:52:42.399Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"62d691c4b51d0658b8f0e5ac2498f107","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:52:42.409Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"775edf40a29bd9dc7c77a6ab4056e893","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:52:42.411Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"775edf40a29bd9dc7c77a6ab4056e893","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:52:42.424Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"56197d191d034647ca4b55614f34cfc4","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:52:42.430Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"56197d191d034647ca4b55614f34cfc4","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:52:42.436Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b70c06d146030106d60c5558c5333411","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:52:42.437Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"b70c06d146030106d60c5558c5333411","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:53:14.345Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"700ac7c15b6b927393d4911ef656e6a7","docName":"simple-test-doc","duration":32903,"totalConnections":5}}
{"timestamp":"2025-07-04T21:53:14.440Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c9ab68ec09c1c2c4d402c80cef55023d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:53:14.440Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c9ab68ec09c1c2c4d402c80cef55023d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:53:42.372Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9619c3fc66b19ca57c341260ef4e5eff","docName":"simple-test-doc","duration":59988,"totalConnections":5}}
{"timestamp":"2025-07-04T21:53:42.373Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"56197d191d034647ca4b55614f34cfc4","docName":"simple-test-doc","duration":59950,"totalConnections":4}}
{"timestamp":"2025-07-04T21:53:42.373Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"62d691c4b51d0658b8f0e5ac2498f107","docName":"simple-test-doc","duration":59975,"totalConnections":3}}
{"timestamp":"2025-07-04T21:53:42.373Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"775edf40a29bd9dc7c77a6ab4056e893","docName":"simple-test-doc","duration":59964,"totalConnections":2}}
{"timestamp":"2025-07-04T21:53:42.374Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"b70c06d146030106d60c5558c5333411","docName":"simple-test-doc","duration":59938,"totalConnections":1}}
{"timestamp":"2025-07-04T21:53:47.337Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c9ab68ec09c1c2c4d402c80cef55023d","docName":"simple-test-doc","duration":32897,"totalConnections":0}}
{"timestamp":"2025-07-04T21:53:47.442Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e59efdb27b9e1a478a11b21a69177ce7","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:53:47.442Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"e59efdb27b9e1a478a11b21a69177ce7","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:54:20.344Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"e59efdb27b9e1a478a11b21a69177ce7","docName":"simple-test-doc","duration":32902,"totalConnections":0}}
{"timestamp":"2025-07-04T21:54:20.452Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c3a06d290d3478945144d05770a57821","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:54:20.452Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"c3a06d290d3478945144d05770a57821","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:54:42.396Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7934af6655bf7cb8e9018d3e9a8710aa","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:54:42.398Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"7934af6655bf7cb8e9018d3e9a8710aa","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:54:42.409Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"242c064b1fac0fc49ed7b48204255c7c","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:54:42.410Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"242c064b1fac0fc49ed7b48204255c7c","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:54:42.423Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"832eb1b750957c8d8723f3d58b6c3df0","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:54:42.424Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"832eb1b750957c8d8723f3d58b6c3df0","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:54:42.435Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d9c0af2f1270816ccb2b956e96882e5f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:54:42.435Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"d9c0af2f1270816ccb2b956e96882e5f","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:54:42.449Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bb9ad40b5b86b303e0f11dc89978ac4d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:54:42.449Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"bb9ad40b5b86b303e0f11dc89978ac4d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:54:53.337Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"c3a06d290d3478945144d05770a57821","docName":"simple-test-doc","duration":32885,"totalConnections":5}}
{"timestamp":"2025-07-04T21:54:53.442Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9f1e43d34156fa31358f3f248935d669","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:54:53.443Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"9f1e43d34156fa31358f3f248935d669","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:55:26.339Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"9f1e43d34156fa31358f3f248935d669","docName":"simple-test-doc","duration":32897,"totalConnections":5}}
{"timestamp":"2025-07-04T21:55:26.445Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"57e34622eb7dbf0ba4740253bb8eef4d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:55:26.445Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"57e34622eb7dbf0ba4740253bb8eef4d","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:55:42.372Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"242c064b1fac0fc49ed7b48204255c7c","docName":"simple-test-doc","duration":59963,"totalConnections":5}}
{"timestamp":"2025-07-04T21:55:42.372Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"d9c0af2f1270816ccb2b956e96882e5f","docName":"simple-test-doc","duration":59937,"totalConnections":4}}
{"timestamp":"2025-07-04T21:55:42.374Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"bb9ad40b5b86b303e0f11dc89978ac4d","docName":"simple-test-doc","duration":59925,"totalConnections":3}}
{"timestamp":"2025-07-04T21:55:42.374Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"7934af6655bf7cb8e9018d3e9a8710aa","docName":"simple-test-doc","duration":59978,"totalConnections":2}}
{"timestamp":"2025-07-04T21:55:42.375Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"832eb1b750957c8d8723f3d58b6c3df0","docName":"simple-test-doc","duration":59952,"totalConnections":1}}
{"timestamp":"2025-07-04T21:55:59.337Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"57e34622eb7dbf0ba4740253bb8eef4d","docName":"simple-test-doc","duration":32892,"totalConnections":0}}
{"timestamp":"2025-07-04T21:55:59.445Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6561b404f772fda53aa2c5f220c1de93","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:55:59.446Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"6561b404f772fda53aa2c5f220c1de93","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:56:32.337Z","level":"info","message":"Connection removed","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"6561b404f772fda53aa2c5f220c1de93","docName":"simple-test-doc","duration":32892,"totalConnections":0}}
{"timestamp":"2025-07-04T21:56:32.444Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"51c130eb27139803e15d348e3e9064b2","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:56:32.445Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"51c130eb27139803e15d348e3e9064b2","docName":"simple-test-doc","totalConnections":1}}
{"timestamp":"2025-07-04T21:56:42.377Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"055c33fe1f6120cda01905ec7adecda6","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:56:42.377Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"055c33fe1f6120cda01905ec7adecda6","docName":"simple-test-doc","totalConnections":2}}
{"timestamp":"2025-07-04T21:56:42.389Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"5ebe551830d977f5d1305b81031612b4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:56:42.390Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"5ebe551830d977f5d1305b81031612b4","docName":"simple-test-doc","totalConnections":3}}
{"timestamp":"2025-07-04T21:56:42.402Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"58a26bbb840955748f504bba749da65f","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:56:42.405Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"58a26bbb840955748f504bba749da65f","docName":"simple-test-doc","totalConnections":4}}
{"timestamp":"2025-07-04T21:56:42.415Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"529958559b97b4af2184fddaaff7ec11","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:56:42.416Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"529958559b97b4af2184fddaaff7ec11","docName":"simple-test-doc","totalConnections":5}}
{"timestamp":"2025-07-04T21:56:42.428Z","level":"info","message":"Connection added","service":"y-websocket-server","component":"ConnectionManager","metadata":{"connectionId":"225030d24499589e07216b5b771f5a95","docName":"simple-test-doc","totalConnections":6}}
{"timestamp":"2025-07-04T21:56:42.429Z","level":"info","message":"WebSocket connection established","service":"y-websocket-server","component":"WebSocketHandler","metadata":{"connectionId":"225030d24499589e07216b5b771f5a95","docName":"simple-test-doc","totalConnections":6}}
