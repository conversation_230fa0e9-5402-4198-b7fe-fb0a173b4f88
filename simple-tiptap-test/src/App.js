import React, { useEffect, useState, useMemo } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Collaboration from '@tiptap/extension-collaboration'
import CollaborationCursor from '@tiptap/extension-collaboration-cursor'
import * as Y from 'yjs'
import { WebsocketProvider } from 'y-websocket'

function App() {
  const [provider, setProvider] = useState(null)
  const [status, setStatus] = useState('connecting')
  const [userName] = useState('User' + Math.floor(Math.random() * 1000))
  const [userColor] = useState('#' + Math.floor(Math.random()*16777215).toString(16))

  // Create Y.js document (memoized to prevent recreation)
  const ydoc = useMemo(() => new Y.Doc(), [])

  useEffect(() => {
    console.log('Setting up WebSocket provider...')

    // Create WebSocket provider
    const wsProvider = new WebsocketProvider(
      'ws://localhost:1234',
      'simple-test-doc',
      ydoc
    )

    // Enhanced status handling
    wsProvider.on('status', (event) => {
      console.log('WebSocket status:', event.status)
      setStatus(event.status)
    })

    setProvider(wsProvider)

    return () => {
      console.log('Cleaning up WebSocket provider...')
      wsProvider.destroy()
    }
  }, []) // Remove ydoc dependency to prevent recreation

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false, // Disable history for Y.js
      }),
      Collaboration.configure({
        document: ydoc,
      }),
      ...(provider ? [CollaborationCursor.configure({
        provider: provider,
        user: {
          name: userName,
          color: userColor,
        },
      })] : []),
    ],
    content: '<p>Start typing to test collaboration!</p>',
  }, [provider])

  if (!editor) {
    return <div>Loading...</div>
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Simple Tiptap Test</h1>

      <div style={{ marginBottom: '10px' }}>
        <strong>Status:</strong> <span style={{
          color: status === 'connected' ? 'green' : status === 'connecting' ? 'orange' : 'red'
        }}>{status}</span> |
        <strong> User:</strong> {userName} |
        <strong> Color:</strong> <span style={{ color: userColor }}>●</span> |
        <strong> Provider:</strong> {provider ? 'Ready' : 'Not Ready'} |
        <strong> Editor:</strong> {editor ? 'Ready' : 'Not Ready'}
      </div>

      <div style={{ marginBottom: '10px' }}>
        <button onClick={() => editor.chain().focus().toggleBold().run()}>
          Bold
        </button>
        <button onClick={() => editor.chain().focus().toggleItalic().run()}>
          Italic
        </button>
        <button onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}>
          H1
        </button>
        <button onClick={() => editor.chain().focus().setParagraph().run()}>
          Paragraph
        </button>
      </div>

      <div style={{
        border: '1px solid #ccc',
        padding: '10px',
        minHeight: '200px',
        backgroundColor: '#fff'
      }}>
        <EditorContent editor={editor} />
      </div>

      <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
        <div>Open this page in multiple tabs to test collaboration!</div>
        <div style={{ marginTop: '5px' }}>
          <button
            onClick={() => {
              if (editor) {
                editor.chain().focus().insertContent(' [Test sync] ').run()
              }
            }}
            style={{ fontSize: '12px', padding: '2px 6px' }}
          >
            Insert Test Text
          </button>
          <span style={{ marginLeft: '10px' }}>
            Document ID: simple-test-doc
          </span>
        </div>
      </div>
    </div>
  )
}

export default App
