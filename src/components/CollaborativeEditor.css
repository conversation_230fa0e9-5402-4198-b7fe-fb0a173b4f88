.collaborative-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 800px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.editor-header {
  flex-shrink: 0;
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.editor-footer {
  flex-shrink: 0;
}

/* Tiptap Editor Styles */
.ProseMirror {
  outline: none;
  min-height: 400px;
  padding: 1rem;
  line-height: 1.6;
}

.ProseMirror h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1rem 0;
  color: #1f2937;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0.75rem 0;
  color: #374151;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 0.5rem 0;
  color: #4b5563;
}

.ProseMirror p {
  margin: 0.5rem 0;
  color: #374151;
}

.ProseMirror ul,
.ProseMirror ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.ProseMirror li {
  margin: 0.25rem 0;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.ProseMirror pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.ProseMirror pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* Collaboration Cursor Styles */
.collaboration-cursor__caret {
  border-left: 1px solid #0d0d0d;
  border-right: 1px solid #0d0d0d;
  margin-left: -1px;
  margin-right: -1px;
  pointer-events: none;
  position: relative;
  word-break: normal;
}

.collaboration-cursor__label {
  border-radius: 3px 3px 3px 0;
  color: #0d0d0d;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  left: -1px;
  line-height: normal;
  padding: 0.1rem 0.3rem;
  position: absolute;
  top: -1.4em;
  user-select: none;
  white-space: nowrap;
}

/* Toolbar Styles */
.toolbar button {
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid transparent;
}

.toolbar button:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.toolbar button:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  ring-opacity: 0.5;
}

/* Status Indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* User Avatar Styles */
.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .collaborative-editor {
    height: 100vh;
    border-radius: 0;
  }
  
  .toolbar {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .toolbar button {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
  
  .editor-header .flex {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}

/* Loading State */
.loading-editor {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

/* Animation for user avatars */
.user-avatar {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Focus styles for better accessibility */
.ProseMirror:focus {
  outline: none;
  box-shadow: inset 0 0 0 2px #3b82f6;
  border-radius: 4px;
}

/* Selection styles */
.ProseMirror ::selection {
  background-color: #bfdbfe;
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}
