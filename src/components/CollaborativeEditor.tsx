import React, { useEffect, useState } from 'react'
import { useE<PERSON><PERSON>, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Collaboration from '@tiptap/extension-collaboration'
import CollaborationCursor from '@tiptap/extension-collaboration-cursor'
import Color from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import TextAlign from '@tiptap/extension-text-align'
import * as Y from 'yjs'
import { WebsocketProvider } from 'y-websocket'
import './CollaborativeEditor.css'

interface CollaborativeEditorProps {
  documentId: string
  userName: string
  userColor: string
  serverUrl?: string
}

const CollaborativeEditor: React.FC<CollaborativeEditorProps> = ({
  documentId,
  userName,
  userColor,
  serverUrl = 'ws://localhost:1234'
}) => {
  const [provider, setProvider] = useState<WebsocketProvider | null>(null)
  const [status, setStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting')
  const [users, setUsers] = useState<any[]>([])

  // Create Y.js document
  const ydoc = new Y.Doc()

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Disable the default history extension since we're using Y.js
        history: false,
      }),
      Collaboration.configure({
        document: ydoc,
      }),
      CollaborationCursor.configure({
        provider: provider as any,
        user: {
          name: userName,
          color: userColor,
        },
      }),
      Color.configure({ types: [TextStyle.name] }),
      TextStyle,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
    ],
    content: `
      <h1>Welcome to Collaborative Editing!</h1>
      <p>This is a collaborative editor powered by Tiptap and Y.js. Start typing to see the magic happen!</p>
      <p>Multiple users can edit this document simultaneously and see each other's changes in real-time.</p>
    `,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
      },
    },
  })

  useEffect(() => {
    // Create WebSocket provider
    const websocketProvider = new WebsocketProvider(
      serverUrl,
      documentId,
      ydoc
    )

    setProvider(websocketProvider)

    // Handle connection status
    websocketProvider.on('status', (event: any) => {
      setStatus(event.status)
    })

    // Handle awareness updates (user cursors)
    websocketProvider.awareness.on('change', () => {
      const states = Array.from(websocketProvider.awareness.getStates().values())
      setUsers(states.filter((state: any) => state.user))
    })

    // Cleanup on unmount
    return () => {
      websocketProvider.destroy()
    }
  }, [documentId, serverUrl, ydoc])

  // Update collaboration cursor provider when provider changes
  useEffect(() => {
    if (editor && provider) {
      const collaborationCursor = editor.extensionManager.extensions.find(
        (ext) => ext.name === 'collaborationCursor'
      )
      if (collaborationCursor) {
        collaborationCursor.options.provider = provider
      }
    }
  }, [editor, provider])

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'text-green-600'
      case 'connecting':
        return 'text-yellow-600'
      case 'disconnected':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return '🟢'
      case 'connecting':
        return '🟡'
      case 'disconnected':
        return '🔴'
      default:
        return '⚪'
    }
  }

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading editor...</div>
      </div>
    )
  }

  return (
    <div className="collaborative-editor">
      {/* Header */}
      <div className="editor-header">
        <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold">Collaborative Editor</h2>
            <div className="flex items-center space-x-2">
              <span className={`text-sm ${getStatusColor()}`}>
                {getStatusIcon()} {status}
              </span>
            </div>
          </div>
          
          {/* Active Users */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Active users:</span>
            <div className="flex space-x-1">
              {users.map((user, index) => (
                <div
                  key={index}
                  className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium"
                  style={{ backgroundColor: user.user.color }}
                  title={user.user.name}
                >
                  {user.user.name.charAt(0).toUpperCase()}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Toolbar */}
        <div className="toolbar flex items-center space-x-2 p-2 bg-white border-b">
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('bold') ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            Bold
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('italic') ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            Italic
          </button>
          <button
            onClick={() => editor.chain().focus().toggleStrike().run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('strike') ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            Strike
          </button>
          <div className="w-px h-6 bg-gray-300"></div>
          <button
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('heading', { level: 1 }) ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            H1
          </button>
          <button
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('heading', { level: 2 }) ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            H2
          </button>
          <button
            onClick={() => editor.chain().focus().setParagraph().run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('paragraph') ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            P
          </button>
          <div className="w-px h-6 bg-gray-300"></div>
          <button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('bulletList') ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            • List
          </button>
          <button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`px-3 py-1 rounded ${
              editor.isActive('orderedList') ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            1. List
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="editor-content">
        <EditorContent editor={editor} />
      </div>

      {/* Footer */}
      <div className="editor-footer p-2 bg-gray-50 border-t text-xs text-gray-500">
        Document ID: {documentId} | User: {userName}
      </div>
    </div>
  )
}

export default CollaborativeEditor
