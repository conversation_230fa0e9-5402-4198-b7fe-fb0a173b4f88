import { Level } from 'level'
import * as Y from 'yjs'
import { IPersistence } from '../interfaces/IPersistence.js'
import logger from './Logger.js'
import config from '../config/index.js'
import path from 'path'
import fs from 'fs/promises'

/**
 * LevelDB persistence implementation
 */
export class LevelDBPersistence extends IPersistence {
  constructor(options = {}) {
    super()
    this.directory = options.directory || config.persistence.directory
    this.db = null
    this.isOpen = false
    this.logger = logger.child({ component: 'LevelDBPersistence' })
  }

  async initialize() {
    try {
      // Ensure directory exists
      await fs.mkdir(this.directory, { recursive: true })
      
      // Open database
      this.db = new Level(this.directory, { 
        valueEncoding: 'buffer',
        keyEncoding: 'utf8'
      })
      
      await this.db.open()
      this.isOpen = true
      
      this.logger.info('LevelDB persistence initialized', { 
        directory: this.directory 
      })
      
    } catch (error) {
      this.logger.error('Failed to initialize LevelDB persistence', error)
      throw error
    }
  }

  bindState(docName, doc) {
    if (!this.isOpen) {
      this.logger.warn('Persistence not initialized, skipping bind state', { docName })
      return
    }

    // Load existing state
    this.loadDocumentState(docName, doc)
      .catch(error => {
        this.logger.error('Error loading document state', { docName, error })
      })
  }

  async loadDocumentState(docName, doc) {
    try {
      const state = await this.readState(docName)
      if (state && state.length > 0) {
        Y.applyUpdate(doc, state)
        this.logger.debug('Document state loaded from persistence', { 
          docName, 
          stateSize: state.length 
        })
      }
    } catch (error) {
      if (error.code !== 'LEVEL_NOT_FOUND') {
        this.logger.error('Error loading document state', { docName, error })
      }
    }
  }

  async writeState(docName, doc) {
    if (!this.isOpen) {
      this.logger.warn('Persistence not initialized, skipping write state', { docName })
      return
    }

    try {
      const state = Y.encodeStateAsUpdate(doc)
      await this.db.put(this.getDocumentKey(docName), Buffer.from(state))
      
      this.logger.debug('Document state written to persistence', { 
        docName, 
        stateSize: state.length 
      })
      
    } catch (error) {
      this.logger.error('Error writing document state', { docName, error })
      throw error
    }
  }

  async readState(docName) {
    if (!this.isOpen) {
      throw new Error('Persistence not initialized')
    }

    try {
      const buffer = await this.db.get(this.getDocumentKey(docName))
      return new Uint8Array(buffer)
    } catch (error) {
      if (error.code === 'LEVEL_NOT_FOUND') {
        return null
      }
      throw error
    }
  }

  async deleteState(docName) {
    if (!this.isOpen) {
      throw new Error('Persistence not initialized')
    }

    try {
      await this.db.del(this.getDocumentKey(docName))
      this.logger.debug('Document state deleted from persistence', { docName })
    } catch (error) {
      if (error.code !== 'LEVEL_NOT_FOUND') {
        this.logger.error('Error deleting document state', { docName, error })
        throw error
      }
    }
  }

  async exists(docName) {
    if (!this.isOpen) {
      return false
    }

    try {
      await this.db.get(this.getDocumentKey(docName))
      return true
    } catch (error) {
      if (error.code === 'LEVEL_NOT_FOUND') {
        return false
      }
      throw error
    }
  }

  async getAllDocuments() {
    if (!this.isOpen) {
      return []
    }

    const documents = []
    const prefix = 'doc:'
    
    try {
      for await (const [key] of this.db.iterator({ 
        gte: prefix, 
        lt: prefix + '\xFF' 
      })) {
        const docName = key.substring(prefix.length)
        documents.push(docName)
      }
    } catch (error) {
      this.logger.error('Error getting all documents', error)
      throw error
    }

    return documents
  }

  async getStats() {
    if (!this.isOpen) {
      return null
    }

    try {
      const stats = {
        documentCount: 0,
        totalSize: 0,
        documents: {}
      }

      const prefix = 'doc:'
      for await (const [key, value] of this.db.iterator({ 
        gte: prefix, 
        lt: prefix + '\xFF' 
      })) {
        const docName = key.substring(prefix.length)
        const size = value.length
        
        stats.documentCount++
        stats.totalSize += size
        stats.documents[docName] = { size }
      }

      return stats
    } catch (error) {
      this.logger.error('Error getting persistence stats', error)
      throw error
    }
  }

  async compact() {
    if (!this.isOpen) {
      throw new Error('Persistence not initialized')
    }

    try {
      // LevelDB doesn't have explicit compaction in the Level package
      // This is handled automatically by the underlying LevelDB
      this.logger.info('Database compaction requested (handled automatically)')
    } catch (error) {
      this.logger.error('Error during compaction', error)
      throw error
    }
  }

  async backup(backupPath) {
    if (!this.isOpen) {
      throw new Error('Persistence not initialized')
    }

    try {
      await fs.mkdir(backupPath, { recursive: true })
      
      // Create a simple backup by copying all key-value pairs
      const backupFile = path.join(backupPath, `backup-${Date.now()}.json`)
      const backup = {}
      
      for await (const [key, value] of this.db.iterator()) {
        backup[key] = value.toString('base64')
      }
      
      await fs.writeFile(backupFile, JSON.stringify(backup, null, 2))
      
      this.logger.info('Database backup created', { backupFile })
      return backupFile
      
    } catch (error) {
      this.logger.error('Error creating backup', error)
      throw error
    }
  }

  async restore(backupFile) {
    if (!this.isOpen) {
      throw new Error('Persistence not initialized')
    }

    try {
      const backupData = await fs.readFile(backupFile, 'utf8')
      const backup = JSON.parse(backupData)
      
      // Clear existing data
      await this.db.clear()
      
      // Restore data
      const batch = this.db.batch()
      for (const [key, value] of Object.entries(backup)) {
        batch.put(key, Buffer.from(value, 'base64'))
      }
      await batch.write()
      
      this.logger.info('Database restored from backup', { backupFile })
      
    } catch (error) {
      this.logger.error('Error restoring from backup', error)
      throw error
    }
  }

  async close() {
    if (this.isOpen && this.db) {
      try {
        await this.db.close()
        this.isOpen = false
        this.logger.info('LevelDB persistence closed')
      } catch (error) {
        this.logger.error('Error closing LevelDB persistence', error)
        throw error
      }
    }
  }

  // Private methods

  getDocumentKey(docName) {
    return `doc:${docName}`
  }
}

export default LevelDBPersistence
