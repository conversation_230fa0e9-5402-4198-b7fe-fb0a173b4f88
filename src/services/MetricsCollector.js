import { IMetricsCollector } from '../interfaces/IMetricsCollector.js'
import logger from './Logger.js'
import config from '../config/index.js'

/**
 * Simple in-memory metrics collector implementation
 * For production, consider using Prometheus client or similar
 */
export class MetricsCollector extends IMetricsCollector {
  constructor(options = {}) {
    super()
    this.enabled = options.enabled !== false && config.monitoring.metricsEnabled
    this.metrics = new Map()
    this.logger = logger.child({ component: 'MetricsCollector' })
    
    if (this.enabled) {
      this.initializeDefaultMetrics()
    }
  }

  initializeDefaultMetrics() {
    // Initialize default metrics
    this.registerMetric('websocket_connections_total', 'counter', 'Total WebSocket connections')
    this.registerMetric('websocket_connections_active', 'gauge', 'Active WebSocket connections')
    this.registerMetric('websocket_messages_received_total', 'counter', 'Total messages received')
    this.registerMetric('websocket_messages_sent_total', 'counter', 'Total messages sent')
    this.registerMetric('websocket_message_size_bytes', 'histogram', 'WebSocket message size in bytes')
    this.registerMetric('documents_created_total', 'counter', 'Total documents created')
    this.registerMetric('documents_removed_total', 'counter', 'Total documents removed')
    this.registerMetric('documents_active', 'gauge', 'Active documents')
    this.registerMetric('documents_memory_bytes', 'gauge', 'Memory used by documents in bytes')
    this.registerMetric('http_requests_total', 'counter', 'Total HTTP requests', ['method', 'status'])
    this.registerMetric('http_request_duration_ms', 'histogram', 'HTTP request duration in milliseconds')
  }

  incrementCounter(name, value = 1, labels = {}) {
    if (!this.enabled) return

    const metric = this.getOrCreateMetric(name, 'counter')
    const key = this.getMetricKey(name, labels)
    
    if (!metric.values.has(key)) {
      metric.values.set(key, { value: 0, labels })
    }
    
    metric.values.get(key).value += value
    metric.lastUpdated = Date.now()
  }

  setGauge(name, value, labels = {}) {
    if (!this.enabled) return

    const metric = this.getOrCreateMetric(name, 'gauge')
    const key = this.getMetricKey(name, labels)
    
    metric.values.set(key, { value, labels })
    metric.lastUpdated = Date.now()
  }

  recordHistogram(name, value, labels = {}) {
    if (!this.enabled) return

    const metric = this.getOrCreateMetric(name, 'histogram')
    const key = this.getMetricKey(name, labels)
    
    if (!metric.values.has(key)) {
      metric.values.set(key, {
        labels,
        count: 0,
        sum: 0,
        buckets: new Map([
          [1, 0], [5, 0], [10, 0], [25, 0], [50, 0], 
          [100, 0], [250, 0], [500, 0], [1000, 0], [2500, 0], 
          [5000, 0], [10000, 0], [Infinity, 0]
        ])
      })
    }
    
    const histogram = metric.values.get(key)
    histogram.count++
    histogram.sum += value
    
    // Update buckets
    for (const [bucket, count] of histogram.buckets) {
      if (value <= bucket) {
        histogram.buckets.set(bucket, count + 1)
      }
    }
    
    metric.lastUpdated = Date.now()
  }

  startTimer(name, labels = {}) {
    if (!this.enabled) {
      return () => {} // Return no-op function
    }

    const startTime = Date.now()
    
    return () => {
      const duration = Date.now() - startTime
      this.recordHistogram(name, duration, labels)
      return duration
    }
  }

  async getMetrics() {
    if (!this.enabled) {
      return ''
    }

    const lines = []
    
    for (const [name, metric] of this.metrics) {
      // Add help and type comments
      lines.push(`# HELP ${name} ${metric.help}`)
      lines.push(`# TYPE ${name} ${metric.type}`)
      
      if (metric.type === 'histogram') {
        this.formatHistogramMetric(name, metric, lines)
      } else {
        this.formatSimpleMetric(name, metric, lines)
      }
      
      lines.push('') // Empty line between metrics
    }
    
    return lines.join('\n')
  }

  formatSimpleMetric(name, metric, lines) {
    for (const [key, data] of metric.values) {
      const labelsStr = this.formatLabels(data.labels)
      lines.push(`${name}${labelsStr} ${data.value}`)
    }
  }

  formatHistogramMetric(name, metric, lines) {
    for (const [key, data] of metric.values) {
      const labelsStr = this.formatLabels(data.labels)
      
      // Add bucket metrics
      for (const [bucket, count] of data.buckets) {
        const bucketLabels = { ...data.labels, le: bucket === Infinity ? '+Inf' : bucket.toString() }
        const bucketLabelsStr = this.formatLabels(bucketLabels)
        lines.push(`${name}_bucket${bucketLabelsStr} ${count}`)
      }
      
      // Add count and sum
      lines.push(`${name}_count${labelsStr} ${data.count}`)
      lines.push(`${name}_sum${labelsStr} ${data.sum}`)
    }
  }

  formatLabels(labels) {
    const labelPairs = Object.entries(labels)
    if (labelPairs.length === 0) {
      return ''
    }
    
    const labelStr = labelPairs
      .map(([key, value]) => `${key}="${value}"`)
      .join(',')
    
    return `{${labelStr}}`
  }

  reset() {
    if (!this.enabled) return

    this.metrics.clear()
    this.initializeDefaultMetrics()
    this.logger.debug('Metrics reset')
  }

  registerMetric(name, type, help, labelNames = []) {
    if (!this.enabled) return

    if (this.metrics.has(name)) {
      this.logger.warn('Metric already registered', { name })
      return
    }

    this.metrics.set(name, {
      name,
      type,
      help,
      labelNames,
      values: new Map(),
      createdAt: Date.now(),
      lastUpdated: Date.now()
    })
  }

  getOrCreateMetric(name, type) {
    let metric = this.metrics.get(name)
    
    if (!metric) {
      this.registerMetric(name, type, `Auto-generated ${type} metric`)
      metric = this.metrics.get(name)
    }
    
    return metric
  }

  getMetricKey(name, labels) {
    const sortedLabels = Object.keys(labels)
      .sort()
      .map(key => `${key}:${labels[key]}`)
      .join(',')
    
    return sortedLabels || 'default'
  }

  // Additional utility methods

  getMetricValue(name, labels = {}) {
    if (!this.enabled) return null

    const metric = this.metrics.get(name)
    if (!metric) return null

    const key = this.getMetricKey(name, labels)
    const data = metric.values.get(key)
    
    return data ? data.value : null
  }

  getAllMetrics() {
    if (!this.enabled) return {}

    const result = {}
    
    for (const [name, metric] of this.metrics) {
      result[name] = {
        type: metric.type,
        help: metric.help,
        values: Array.from(metric.values.entries()).map(([key, data]) => ({
          labels: data.labels,
          value: data.value
        }))
      }
    }
    
    return result
  }

  getStats() {
    return {
      enabled: this.enabled,
      totalMetrics: this.metrics.size,
      totalValues: Array.from(this.metrics.values())
        .reduce((sum, metric) => sum + metric.values.size, 0)
    }
  }
}

export default MetricsCollector
