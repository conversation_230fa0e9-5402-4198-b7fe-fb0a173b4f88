import * as Y from 'yjs'
import * as syncProtocol from '@y/protocols/sync'
import * as awarenessProtocol from '@y/protocols/awareness'
import * as encoding from 'lib0/encoding'
import * as decoding from 'lib0/decoding'
import logger from './Logger.js'
import config from '../config/index.js'

/**
 * WebSocket message handler following the Single Responsibility Principle
 */
export class WebSocketHandler {
  constructor(options = {}) {
    this.connectionManager = options.connectionManager
    this.documentManager = options.documentManager
    this.authProvider = options.authProvider
    this.securityMiddleware = options.securityMiddleware

    this.pingTimeout = options.pingTimeout || config.websocket.pingTimeout
    this.logger = logger.child({ component: 'WebSocketHandler' })
    
    // Message type constants
    this.MESSAGE_SYNC = 0
    this.MESSAGE_AWARENESS = 1
    this.MESSAGE_AUTH = 2
    
    // WebSocket ready states
    this.WS_CONNECTING = 0
    this.WS_OPEN = 1
    this.WS_CLOSING = 2
    this.WS_CLOSED = 3
  }

  async setupConnection(connection, request, options = {}) {
    try {
      const connectionId = this.securityMiddleware.generateConnectionId()
      const docName = options.docName || this.securityMiddleware.extractDocumentName(request.url || '')
      
      if (!docName) {
        this.logger.warn('Invalid document name', { url: request.url })
        connection.close(1008, 'Invalid document name')
        return
      }

      // Authenticate if enabled
      let user = null
      if (config.auth.enabled && this.authProvider) {
        user = await this.authProvider.authenticate(request)
        if (!user) {
          this.logger.warn('Authentication failed', { docName, connectionId })
          connection.close(1008, 'Authentication required')
          return
        }

        // Authorize access to document
        const authorized = await this.authProvider.authorize(user, docName, 'read')
        if (!authorized) {
          this.logger.warn('Authorization failed', { docName, connectionId, userId: user.id })
          connection.close(1008, 'Access denied')
          return
        }
      }

      // Setup connection
      connection.binaryType = 'arraybuffer'
      
      // Get or create document
      const doc = this.documentManager.getDocument(docName, options)
      
      // Add connection to manager
      this.connectionManager.addConnection(connectionId, connection, {
        docName,
        user,
        request: {
          url: request.url,
          headers: request.headers,
          remoteAddress: request.socket.remoteAddress
        }
      })

      // Add connection to document
      doc.conns.set(connection, new Set())

      // Setup message handler
      connection.on('message', (message) => {
        this.handleMessage(connectionId, connection, doc, new Uint8Array(message))
      })

      // Setup ping/pong for connection health
      this.setupPingPong(connectionId, connection, doc)

      // Setup close handler
      connection.on('close', () => {
        this.handleConnectionClose(connectionId, connection, doc)
      })

      // Setup error handler
      connection.on('error', (error) => {
        this.handleConnectionError(connectionId, connection, doc, error)
      })

      // Send initial sync
      await this.sendInitialSync(connection, doc)

      this.logger.info('WebSocket connection established', {
        connectionId,
        docName,
        userId: user?.id,
        totalConnections: this.connectionManager.getConnectionCount()
      })



    } catch (error) {
      this.logger.error('Failed to setup WebSocket connection', error)
      connection.close(1011, 'Internal server error')
    }
  }

  handleMessage(connectionId, connection, doc, message) {
    try {
      // Validate message
      const validation = this.securityMiddleware.validateMessage(message, { id: connectionId })
      if (!validation.valid) {
        this.logger.warn('Invalid message received', {
          connectionId,
          reason: validation.reason
        })
        return
      }

      // Update connection activity
      this.connectionManager.updateConnectionMetadata(connectionId, {
        lastMessageAt: Date.now()
      })

      // Process message
      this.processMessage(connectionId, connection, doc, message)



    } catch (error) {
      this.logger.error('Error handling message', { connectionId, error })
      this.handleConnectionError(connectionId, connection, doc, error)
    }
  }

  processMessage(connectionId, connection, doc, message) {
    const encoder = encoding.createEncoder()
    const decoder = decoding.createDecoder(message)
    const messageType = decoding.readVarUint(decoder)

    switch (messageType) {
      case this.MESSAGE_SYNC:
        this.handleSyncMessage(encoder, decoder, doc, connection)
        break
      case this.MESSAGE_AWARENESS:
        this.handleAwarenessMessage(decoder, doc, connection)
        break
      case this.MESSAGE_AUTH:
        this.handleAuthMessage(decoder, connection, connectionId)
        break
      default:
        this.logger.warn('Unknown message type', { messageType, connectionId })
        return
    }

    // Send response if encoder has content
    if (encoding.length(encoder) > 1) {
      this.sendMessage(connection, doc, encoding.toUint8Array(encoder))
    }
  }

  handleSyncMessage(encoder, decoder, doc, connection) {
    encoding.writeVarUint(encoder, this.MESSAGE_SYNC)
    syncProtocol.readSyncMessage(decoder, encoder, doc, connection)
  }

  handleAwarenessMessage(decoder, doc, connection) {
    awarenessProtocol.applyAwarenessUpdate(
      doc.awareness, 
      decoding.readVarUint8Array(decoder), 
      connection
    )
  }

  handleAuthMessage(decoder, connection, connectionId) {
    // Handle authentication messages if needed
    this.logger.debug('Auth message received', { connectionId })
  }

  sendMessage(connection, doc, message) {
    if (connection.readyState !== this.WS_OPEN) {
      this.closeConnection(doc, connection)
      return
    }

    try {
      connection.send(message, {}, (error) => {
        if (error) {
          this.closeConnection(doc, connection)
        }
      })



    } catch (error) {
      this.logger.error('Error sending message', error)
      this.closeConnection(doc, connection)
    }
  }

  async sendInitialSync(connection, doc) {
    // Wait for document initialization
    await doc.whenInitialized

    // Send sync step 1
    const encoder = encoding.createEncoder()
    encoding.writeVarUint(encoder, this.MESSAGE_SYNC)
    syncProtocol.writeSyncStep1(encoder, doc)
    this.sendMessage(connection, doc, encoding.toUint8Array(encoder))

    // Send awareness states
    const awarenessStates = doc.awareness.getStates()
    if (awarenessStates.size > 0) {
      const awarenessEncoder = encoding.createEncoder()
      encoding.writeVarUint(awarenessEncoder, this.MESSAGE_AWARENESS)
      encoding.writeVarUint8Array(
        awarenessEncoder,
        awarenessProtocol.encodeAwarenessUpdate(
          doc.awareness,
          Array.from(awarenessStates.keys())
        )
      )
      this.sendMessage(connection, doc, encoding.toUint8Array(awarenessEncoder))
    }
  }

  setupPingPong(connectionId, connection, doc) {
    let pongReceived = true
    
    const pingInterval = setInterval(() => {
      if (!pongReceived) {
        this.logger.debug('Ping timeout, closing connection', { connectionId })
        this.closeConnection(doc, connection)
        clearInterval(pingInterval)
      } else if (doc.conns.has(connection)) {
        pongReceived = false
        try {
          connection.ping()
        } catch (error) {
          this.logger.error('Error sending ping', { connectionId, error })
          this.closeConnection(doc, connection)
          clearInterval(pingInterval)
        }
      }
    }, this.pingTimeout)

    connection.on('pong', () => {
      pongReceived = true
    })

    connection.on('close', () => {
      clearInterval(pingInterval)
    })
  }

  handleConnectionClose(connectionId, connection, doc) {
    this.logger.debug('WebSocket connection closed', { connectionId })
    this.closeConnection(doc, connection)
  }

  handleConnectionError(connectionId, connection, doc, error) {
    this.logger.error('WebSocket connection error', { connectionId, error })
    this.closeConnection(doc, connection)
  }

  closeConnection(doc, connection) {
    if (doc.conns.has(connection)) {
      const controlledIds = doc.conns.get(connection)
      doc.conns.delete(connection)
      
      // Remove awareness states
      awarenessProtocol.removeAwarenessStates(
        doc.awareness,
        Array.from(controlledIds),
        null
      )
    }

    try {
      if (connection.readyState === this.WS_OPEN) {
        connection.close()
      }
    } catch (error) {
      this.logger.error('Error closing connection', error)
    }


  }
}

export default WebSocketHandler
