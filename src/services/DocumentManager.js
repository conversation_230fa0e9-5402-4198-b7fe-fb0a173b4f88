import * as Y from 'yjs'
import * as awarenessProtocol from '@y/protocols/awareness'
import * as eventloop from 'lib0/eventloop'
import { IDocumentManager } from '../interfaces/IDocumentManager.js'
import logger from './Logger.js'
import config from '../config/index.js'

/**
 * Enhanced Y.js document class with additional metadata
 */
export class WSSharedDoc extends Y.Doc {
  constructor(name, options = {}) {
    super({ gc: options.gc !== false })
    this.name = name
    this.conns = new Map()
    this.awareness = new awarenessProtocol.Awareness(this)
    this.awareness.setLocalState(null)
    this.createdAt = Date.now()
    this.lastActivity = Date.now()
    this.messageCount = 0
    this.connectionCount = 0
    this.logger = logger.child({ component: 'WSSharedDoc', docName: name })
    
    // Setup awareness change handler
    this.awareness.on('update', this.handleAwarenessChange.bind(this))
    
    // Setup document update handler
    this.on('update', this.handleDocumentUpdate.bind(this))
    
    // Initialize content if initializer is provided
    this.whenInitialized = this.initializeContent(options.contentInitializer)
  }

  async initializeContent(initializer) {
    if (typeof initializer === 'function') {
      try {
        await initializer(this)
        this.logger.debug('Document content initialized')
      } catch (error) {
        this.logger.error('Error initializing document content', error)
      }
    }
  }

  handleAwarenessChange({ added, updated, removed }, conn) {
    const changedClients = added.concat(updated, removed)
    if (conn !== null) {
      const connControlledIDs = this.conns.get(conn)
      if (connControlledIDs !== undefined) {
        added.forEach(clientID => connControlledIDs.add(clientID))
        removed.forEach(clientID => connControlledIDs.delete(clientID))
      }
    }
    
    this.lastActivity = Date.now()
    this.broadcastAwarenessUpdate(changedClients)
  }

  handleDocumentUpdate(update, origin, doc, transaction) {
    this.lastActivity = Date.now()
    this.messageCount++
    this.broadcastUpdate(update)
  }

  broadcastUpdate(update) {
    // This will be implemented by the WebSocketHandler
    // when it sets up the document
  }

  broadcastAwarenessUpdate(changedClients) {
    // This will be implemented by the WebSocketHandler
    // when it sets up the document
  }

  getStats() {
    return {
      name: this.name,
      createdAt: this.createdAt,
      lastActivity: this.lastActivity,
      messageCount: this.messageCount,
      connectionCount: this.conns.size,
      awarenessStates: this.awareness.getStates().size,
      documentSize: this.getUpdate().length
    }
  }

  cleanup() {
    this.logger.debug('Cleaning up document')
    this.conns.clear()
    this.awareness.destroy()
    this.destroy()
  }
}

/**
 * Document manager implementation following the Single Responsibility Principle
 */
export class DocumentManager extends IDocumentManager {
  constructor(options = {}) {
    super()
    this.documents = new Map()
    this.persistence = options.persistence
    this.callbackHandler = options.callbackHandler
    this.metricsCollector = options.metricsCollector
    this.contentInitializer = options.contentInitializer
    this.gcEnabled = options.gcEnabled !== false
    this.maxIdleTime = options.maxIdleTime || 30 * 60 * 1000 // 30 minutes
    this.logger = logger.child({ component: 'DocumentManager' })
    
    // Setup debounced callback handler
    if (this.callbackHandler && this.callbackHandler.isCallbackSet()) {
      this.debouncer = eventloop.createDebouncer(
        config.callback.debounceWait,
        config.callback.debounceMaxWait
      )
    }

    // Setup cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.performCleanup()
    }, 5 * 60 * 1000) // Every 5 minutes
  }

  getDocument(docName, options = {}) {
    try {
      let doc = this.documents.get(docName)
      
      if (!doc) {
        doc = this.createDocument(docName, options)
        this.documents.set(docName, doc)
        
        this.logger.info('Document created', {
          docName,
          totalDocuments: this.documents.size
        })

        // Update metrics
        if (this.metricsCollector) {
          this.metricsCollector.incrementCounter('documents_created_total')
          this.metricsCollector.setGauge('documents_active', this.documents.size)
        }
      }

      // Update last activity
      doc.lastActivity = Date.now()
      
      return doc
    } catch (error) {
      this.logger.error('Error getting document', { docName, error })
      throw error
    }
  }

  createDocument(docName, options = {}) {
    const doc = new WSSharedDoc(docName, {
      gc: options.gc !== undefined ? options.gc : this.gcEnabled,
      contentInitializer: this.contentInitializer
    })

    // Setup persistence if available
    if (this.persistence) {
      this.persistence.bindState(docName, doc)
    }

    // Setup callback handler if available
    if (this.callbackHandler && this.callbackHandler.isCallbackSet()) {
      doc.on('update', () => {
        this.debouncer(() => this.callbackHandler.handleCallback(doc))
      })
    }

    return doc
  }

  hasDocument(docName) {
    return this.documents.has(docName)
  }

  async removeDocument(docName) {
    try {
      const doc = this.documents.get(docName)
      if (!doc) {
        this.logger.debug('Document not found for removal', { docName })
        return
      }

      // Save to persistence before removal
      if (this.persistence && doc.conns.size === 0) {
        await this.persistence.writeState(docName, doc)
      }

      // Cleanup document
      doc.cleanup()
      this.documents.delete(docName)

      this.logger.info('Document removed', {
        docName,
        totalDocuments: this.documents.size
      })

      // Update metrics
      if (this.metricsCollector) {
        this.metricsCollector.incrementCounter('documents_removed_total')
        this.metricsCollector.setGauge('documents_active', this.documents.size)
      }

    } catch (error) {
      this.logger.error('Error removing document', { docName, error })
      throw error
    }
  }

  getAllDocumentNames() {
    return Array.from(this.documents.keys())
  }

  getDocumentStats(docName) {
    const doc = this.documents.get(docName)
    return doc ? doc.getStats() : null
  }

  async cleanupInactiveDocuments(maxIdleTime = this.maxIdleTime) {
    const now = Date.now()
    const inactiveDocuments = []

    for (const [docName, doc] of this.documents) {
      // Only cleanup documents with no active connections
      if (doc.conns.size === 0 && (now - doc.lastActivity) > maxIdleTime) {
        inactiveDocuments.push(docName)
      }
    }

    if (inactiveDocuments.length > 0) {
      this.logger.info('Cleaning up inactive documents', {
        count: inactiveDocuments.length,
        documents: inactiveDocuments
      })

      for (const docName of inactiveDocuments) {
        await this.removeDocument(docName)
      }
    }

    return inactiveDocuments
  }

  getDocumentCount() {
    return this.documents.size
  }

  setContentInitializer(initializer) {
    this.contentInitializer = initializer
  }

  async closeAllDocuments() {
    this.logger.info('Closing all documents', { count: this.documents.size })

    const promises = []
    for (const [docName, doc] of this.documents) {
      promises.push(this.removeDocument(docName))
    }

    await Promise.all(promises)
    this.documents.clear()
  }

  // Statistics and monitoring methods

  getAllDocumentStats() {
    const stats = {
      totalDocuments: this.documents.size,
      totalConnections: 0,
      totalMessages: 0,
      documents: {}
    }

    for (const [docName, doc] of this.documents) {
      const docStats = doc.getStats()
      stats.documents[docName] = docStats
      stats.totalConnections += docStats.connectionCount
      stats.totalMessages += docStats.messageCount
    }

    return stats
  }

  getMemoryUsage() {
    let totalSize = 0
    const documentSizes = {}

    for (const [docName, doc] of this.documents) {
      const size = doc.getUpdate().length
      documentSizes[docName] = size
      totalSize += size
    }

    return {
      totalSize,
      documentSizes,
      documentCount: this.documents.size
    }
  }

  // Private methods

  async performCleanup() {
    try {
      const cleanedUp = await this.cleanupInactiveDocuments()
      
      if (cleanedUp.length > 0) {
        this.logger.debug('Cleanup completed', { 
          cleanedDocuments: cleanedUp.length,
          remainingDocuments: this.documents.size
        })
      }

      // Update metrics
      if (this.metricsCollector) {
        this.metricsCollector.setGauge('documents_active', this.documents.size)
        
        // Memory usage metrics
        const memoryUsage = this.getMemoryUsage()
        this.metricsCollector.setGauge('documents_memory_bytes', memoryUsage.totalSize)
      }

    } catch (error) {
      this.logger.error('Error during cleanup', error)
    }
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    
    // Close all documents
    this.closeAllDocuments()
  }
}

export default DocumentManager
