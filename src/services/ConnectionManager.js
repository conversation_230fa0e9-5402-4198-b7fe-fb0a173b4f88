import { IConnectionManager } from '../interfaces/IConnectionManager.js'
import logger from './Logger.js'
import config from '../config/index.js'

/**
 * WebSocket connection manager implementation
 */
export class ConnectionManager extends IConnectionManager {
  constructor(options = {}) {
    super()
    this.connections = new Map()
    this.documentConnections = new Map()
    this.connectionMetadata = new Map()
    this.maxConnections = options.maxConnections || config.websocket.maxConnections
    this.connectionTimeout = options.connectionTimeout || config.websocket.connectionTimeout
    this.logger = logger.child({ component: 'ConnectionManager' })
    
    // Setup cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleConnections()
    }, 30000) // Check every 30 seconds
  }

  addConnection(connectionId, connection, metadata = {}) {
    try {
      // Check connection limit
      if (this.connections.size >= this.maxConnections) {
        this.logger.warn('Maximum connections reached', {
          current: this.connections.size,
          max: this.maxConnections
        })
        throw new Error('Maximum connections reached')
      }

      // Store connection
      this.connections.set(connectionId, connection)
      
      // Store metadata with timestamps
      const connectionMetadata = {
        ...metadata,
        connectionId,
        connectedAt: Date.now(),
        lastActivity: Date.now(),
        messageCount: 0,
        bytesReceived: 0,
        bytesSent: 0
      }
      this.connectionMetadata.set(connectionId, connectionMetadata)

      // Add to document connections if docName is provided
      if (metadata.docName) {
        this.addToDocumentConnections(metadata.docName, connectionId)
      }

      this.logger.info('Connection added', {
        connectionId,
        docName: metadata.docName,
        totalConnections: this.connections.size
      })

      // Setup connection event handlers
      this.setupConnectionHandlers(connectionId, connection)

    } catch (error) {
      this.logger.error('Failed to add connection', error)
      throw error
    }
  }

  removeConnection(connectionId) {
    try {
      const connection = this.connections.get(connectionId)
      const metadata = this.connectionMetadata.get(connectionId)

      if (!connection) {
        this.logger.debug('Connection not found for removal', { connectionId })
        return
      }

      // Remove from document connections
      if (metadata && metadata.docName) {
        this.removeFromDocumentConnections(metadata.docName, connectionId)
      }

      // Clean up
      this.connections.delete(connectionId)
      this.connectionMetadata.delete(connectionId)

      this.logger.info('Connection removed', {
        connectionId,
        docName: metadata?.docName,
        duration: metadata ? Date.now() - metadata.connectedAt : 0,
        totalConnections: this.connections.size
      })

    } catch (error) {
      this.logger.error('Failed to remove connection', error)
    }
  }

  getConnection(connectionId) {
    return this.connections.get(connectionId) || null
  }

  getDocumentConnections(docName) {
    const connectionIds = this.documentConnections.get(docName) || new Set()
    const connections = new Map()
    
    for (const connectionId of connectionIds) {
      const connection = this.connections.get(connectionId)
      if (connection) {
        connections.set(connectionId, connection)
      }
    }
    
    return connections
  }

  getConnectionCount() {
    return this.connections.size
  }

  hasConnection(connectionId) {
    return this.connections.has(connectionId)
  }

  getConnectionMetadata(connectionId) {
    return this.connectionMetadata.get(connectionId) || null
  }

  updateConnectionMetadata(connectionId, metadata) {
    const existing = this.connectionMetadata.get(connectionId)
    if (existing) {
      this.connectionMetadata.set(connectionId, {
        ...existing,
        ...metadata,
        lastActivity: Date.now()
      })
    }
  }

  closeAllConnections() {
    this.logger.info('Closing all connections', { count: this.connections.size })
    
    for (const [connectionId, connection] of this.connections) {
      try {
        if (connection.readyState === 1) { // OPEN
          connection.close(1000, 'Server shutdown')
        }
      } catch (error) {
        this.logger.error('Error closing connection', { connectionId, error })
      }
    }
    
    this.connections.clear()
    this.connectionMetadata.clear()
    this.documentConnections.clear()
  }

  // Private methods

  addToDocumentConnections(docName, connectionId) {
    if (!this.documentConnections.has(docName)) {
      this.documentConnections.set(docName, new Set())
    }
    this.documentConnections.get(docName).add(connectionId)
  }

  removeFromDocumentConnections(docName, connectionId) {
    const connections = this.documentConnections.get(docName)
    if (connections) {
      connections.delete(connectionId)
      if (connections.size === 0) {
        this.documentConnections.delete(docName)
      }
    }
  }

  setupConnectionHandlers(connectionId, connection) {
    // Track message activity
    const originalSend = connection.send.bind(connection)
    connection.send = (data, options, callback) => {
      this.updateConnectionActivity(connectionId, 'send', data.length)
      return originalSend(data, options, callback)
    }

    // Handle connection close
    connection.on('close', () => {
      this.removeConnection(connectionId)
    })

    // Handle connection errors
    connection.on('error', (error) => {
      this.logger.error('Connection error', { connectionId, error })
      this.removeConnection(connectionId)
    })
  }

  updateConnectionActivity(connectionId, type, bytes = 0) {
    const metadata = this.connectionMetadata.get(connectionId)
    if (metadata) {
      metadata.lastActivity = Date.now()
      metadata.messageCount++
      
      if (type === 'send') {
        metadata.bytesSent += bytes
      } else if (type === 'receive') {
        metadata.bytesReceived += bytes
      }
    }
  }

  cleanupStaleConnections() {
    const now = Date.now()
    const staleConnections = []

    for (const [connectionId, metadata] of this.connectionMetadata) {
      const connection = this.connections.get(connectionId)
      
      // Check if connection is stale
      if (!connection || 
          connection.readyState !== 1 || // Not OPEN
          (now - metadata.lastActivity) > this.connectionTimeout) {
        staleConnections.push(connectionId)
      }
    }

    if (staleConnections.length > 0) {
      this.logger.info('Cleaning up stale connections', { 
        count: staleConnections.length 
      })
      
      staleConnections.forEach(connectionId => {
        this.removeConnection(connectionId)
      })
    }
  }

  // Statistics and monitoring methods

  getConnectionStats() {
    const stats = {
      totalConnections: this.connections.size,
      documentCount: this.documentConnections.size,
      maxConnections: this.maxConnections,
      connectionsByDocument: {}
    }

    for (const [docName, connections] of this.documentConnections) {
      stats.connectionsByDocument[docName] = connections.size
    }

    return stats
  }

  getDetailedStats() {
    const now = Date.now()
    let totalMessages = 0
    let totalBytesReceived = 0
    let totalBytesSent = 0
    const connectionAges = []

    for (const metadata of this.connectionMetadata.values()) {
      totalMessages += metadata.messageCount
      totalBytesReceived += metadata.bytesReceived
      totalBytesSent += metadata.bytesSent
      connectionAges.push(now - metadata.connectedAt)
    }

    return {
      ...this.getConnectionStats(),
      totalMessages,
      totalBytesReceived,
      totalBytesSent,
      averageConnectionAge: connectionAges.length > 0 
        ? connectionAges.reduce((a, b) => a + b, 0) / connectionAges.length 
        : 0
    }
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.closeAllConnections()
  }
}

export default ConnectionManager
