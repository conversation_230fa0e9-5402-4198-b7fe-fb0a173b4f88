import jwt from 'jsonwebtoken'
import { IAuthProvider } from '../interfaces/IAuthProvider.js'
import config from '../config/index.js'
import logger from './Logger.js'

/**
 * JWT-based authentication provider implementation
 */
export class JWTAuthProvider extends IAuthProvider {
  constructor(options = {}) {
    super()
    this.secret = options.secret || config.security.jwtSecret
    this.algorithm = options.algorithm || 'HS256'
    this.expiresIn = options.expiresIn || '24h'
    this.logger = logger.child({ component: 'JWTAuthProvider' })
  }

  async authenticate(request) {
    try {
      const token = this.extractToken(request)
      if (!token) {
        this.logger.debug('No token found in request')
        return null
      }

      const user = await this.validateToken(token)
      if (user) {
        this.logger.debug('User authenticated successfully', { userId: user.id })
      }
      
      return user
    } catch (error) {
      this.logger.error('Authentication failed', error)
      return null
    }
  }

  async authorize(user, docName, action) {
    try {
      if (!user) {
        this.logger.debug('Authorization failed: no user provided')
        return false
      }

      // Basic authorization logic - can be extended
      // Check if user has access to the document
      if (user.permissions && user.permissions.includes('admin')) {
        return true
      }

      // Check document-specific permissions
      if (user.documents && user.documents.includes(docName)) {
        return true
      }

      // Check action-specific permissions
      if (action === 'read' && user.permissions && user.permissions.includes('read')) {
        return true
      }

      if (action === 'write' && user.permissions && user.permissions.includes('write')) {
        return true
      }

      this.logger.debug('Authorization failed', { 
        userId: user.id, 
        docName, 
        action,
        permissions: user.permissions 
      })
      
      return false
    } catch (error) {
      this.logger.error('Authorization error', error)
      return false
    }
  }

  async validateToken(token) {
    try {
      const decoded = jwt.verify(token, this.secret, { algorithm: this.algorithm })
      
      // Check if token is expired
      if (decoded.exp && Date.now() >= decoded.exp * 1000) {
        this.logger.debug('Token expired')
        return null
      }

      return {
        id: decoded.sub || decoded.userId,
        username: decoded.username,
        email: decoded.email,
        permissions: decoded.permissions || [],
        documents: decoded.documents || [],
        iat: decoded.iat,
        exp: decoded.exp
      }
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        this.logger.debug('Invalid token', { error: error.message })
      } else if (error.name === 'TokenExpiredError') {
        this.logger.debug('Token expired', { error: error.message })
      } else {
        this.logger.error('Token validation error', error)
      }
      return null
    }
  }

  async generateToken(user) {
    try {
      const payload = {
        sub: user.id,
        username: user.username,
        email: user.email,
        permissions: user.permissions || [],
        documents: user.documents || [],
        iat: Math.floor(Date.now() / 1000)
      }

      const token = jwt.sign(payload, this.secret, {
        algorithm: this.algorithm,
        expiresIn: this.expiresIn
      })

      this.logger.debug('Token generated successfully', { userId: user.id })
      return token
    } catch (error) {
      this.logger.error('Token generation failed', error)
      throw error
    }
  }

  async refreshToken(token) {
    try {
      const decoded = jwt.verify(token, this.secret, { 
        algorithm: this.algorithm,
        ignoreExpiration: true 
      })

      // Check if token is too old to refresh (e.g., more than 7 days)
      const maxRefreshAge = 7 * 24 * 60 * 60 // 7 days in seconds
      if (Date.now() / 1000 - decoded.iat > maxRefreshAge) {
        this.logger.debug('Token too old to refresh')
        return null
      }

      const user = {
        id: decoded.sub || decoded.userId,
        username: decoded.username,
        email: decoded.email,
        permissions: decoded.permissions || [],
        documents: decoded.documents || []
      }

      return await this.generateToken(user)
    } catch (error) {
      this.logger.error('Token refresh failed', error)
      return null
    }
  }

  extractToken(request) {
    const authHeader = request.headers[config.auth.header.toLowerCase()]
    
    if (!authHeader) {
      return null
    }

    // Support both "Bearer token" and "token" formats
    if (authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7)
    }

    return authHeader
  }
}
