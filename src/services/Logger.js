import winston from 'winston'
import path from 'path'
import { fileURLToPath } from 'url'
import { ILogger } from '../interfaces/ILogger.js'
import config from '../config/index.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * Winston-based logger implementation following the Dependency Inversion Principle
 */
export class Logger extends ILogger {
  constructor(options = {}) {
    super()
    this.context = options.context || {}
    this.winston = this.createWinstonLogger(options)
  }

  createWinstonLogger(options) {
    const logFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        const logEntry = {
          timestamp,
          level,
          message,
          ...this.context,
          ...meta
        }

        if (stack) {
          logEntry.stack = stack
        }

        return JSON.stringify(logEntry)
      })
    )

    const transports = [
      new winston.transports.Console({
        level: config.logging.level,
        format: config.server.isDevelopment
          ? winston.format.combine(
              winston.format.colorize(),
              winston.format.simple()
            )
          : logFormat
      })
    ]

    // Add file transport if configured
    if (config.logging.file) {
      transports.push(
        new winston.transports.File({
          filename: config.logging.file,
          level: config.logging.level,
          format: logFormat,
          maxsize: this.parseSize(config.logging.maxSize),
          maxFiles: config.logging.maxFiles,
          tailable: true
        })
      )
    }

    return winston.createLogger({
      level: config.logging.level,
      format: logFormat,
      transports,
      exitOnError: false
    })
  }

  parseSize(sizeStr) {
    const units = { k: 1024, m: 1024 * 1024, g: 1024 * 1024 * 1024 }
    const match = sizeStr.toLowerCase().match(/^(\d+)([kmg]?)$/)
    if (!match) return 10 * 1024 * 1024 // Default 10MB
    
    const [, size, unit] = match
    return parseInt(size) * (units[unit] || 1)
  }

  error(message, error) {
    const meta = {}
    if (error instanceof Error) {
      meta.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    } else if (error && typeof error === 'object') {
      meta.metadata = error
    }
    
    this.winston.error(message, meta)
  }

  warn(message, metadata = {}) {
    this.winston.warn(message, { metadata })
  }

  info(message, metadata = {}) {
    this.winston.info(message, { metadata })
  }

  debug(message, metadata = {}) {
    this.winston.debug(message, { metadata })
  }

  child(context) {
    return new Logger({
      context: { ...this.context, ...context }
    })
  }

  // Additional utility methods
  profile(id) {
    this.winston.profile(id)
  }

  startTimer() {
    return this.winston.startTimer()
  }

  // Method to handle uncaught exceptions and unhandled rejections
  static setupGlobalErrorHandling(logger) {
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', error)
      process.exit(1)
    })

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection', {
        reason: reason instanceof Error ? reason.message : reason,
        stack: reason instanceof Error ? reason.stack : undefined,
        promise: promise.toString()
      })
    })
  }
}

// Create and export default logger instance
export const logger = new Logger({
  context: { service: 'y-websocket-server' }
})

// Setup global error handling
Logger.setupGlobalErrorHandling(logger)

export default logger
