import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') })

/**
 * Application configuration
 */
export const config = {
  // Server Configuration
  server: {
    nodeEnv: process.env.NODE_ENV || 'development',
    host: process.env.HOST || 'localhost',
    port: parseInt(process.env.PORT || '1234', 10),
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production'
  },

  // Security Configuration
  security: {
    jwtSecret: process.env.JWT_SECRET || 'default-secret-change-in-production',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10)
  },

  // WebSocket Configuration
  websocket: {
    pingTimeout: parseInt(process.env.WS_PING_TIMEOUT || '30000', 10),
    maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS || '1000', 10),
    connectionTimeout: parseInt(process.env.WS_CONNECTION_TIMEOUT || '60000', 10)
  },

  // Persistence Configuration
  persistence: {
    directory: process.env.YPERSISTENCE || './data',
    gcEnabled: process.env.GC !== 'false' && process.env.GC !== '0'
  },

  // Callback Configuration
  callback: {
    url: process.env.CALLBACK_URL || null,
    debounceWait: parseInt(process.env.CALLBACK_DEBOUNCE_WAIT || '2000', 10),
    debounceMaxWait: parseInt(process.env.CALLBACK_DEBOUNCE_MAXWAIT || '10000', 10),
    timeout: parseInt(process.env.CALLBACK_TIMEOUT || '5000', 10),
    objects: process.env.CALLBACK_OBJECTS ? JSON.parse(process.env.CALLBACK_OBJECTS) : {}
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    file: process.env.LOG_FILE || './logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5', 10)
  },

  // Monitoring Configuration
  monitoring: {
    metricsEnabled: process.env.METRICS_ENABLED === 'true',
    healthCheckEnabled: process.env.HEALTH_CHECK_ENABLED !== 'false',
    healthCheckPath: process.env.HEALTH_CHECK_PATH || '/health'
  },

  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    name: process.env.DB_NAME || 'yjs_websocket',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    ssl: process.env.DB_SSL === 'true'
  },

  // Redis Configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || null,
    db: parseInt(process.env.REDIS_DB || '0', 10)
  },

  // Authentication Configuration
  auth: {
    enabled: process.env.AUTH_ENABLED === 'true',
    provider: process.env.AUTH_PROVIDER || 'jwt',
    header: process.env.AUTH_HEADER || 'authorization'
  }
}

export default config
