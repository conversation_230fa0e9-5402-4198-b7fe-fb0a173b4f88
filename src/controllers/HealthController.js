import logger from '../services/Logger.js'
import config from '../config/index.js'

/**
 * Health check controller for monitoring and load balancer health checks
 */
export class HealthController {
  constructor(options = {}) {
    this.connectionManager = options.connectionManager
    this.documentManager = options.documentManager
    this.persistence = options.persistence

    this.logger = logger.child({ component: 'HealthController' })
    this.startTime = Date.now()
  }

  /**
   * Basic health check endpoint
   */
  async getHealth(request, response) {
    try {
      const health = await this.checkHealth()
      
      const statusCode = health.status === 'healthy' ? 200 : 503
      
      response.writeHead(statusCode, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify(health, null, 2))
      
    } catch (error) {
      this.logger.error('Health check failed', error)
      
      response.writeHead(500, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify({
        status: 'error',
        message: 'Health check failed',
        timestamp: new Date().toISOString()
      }))
    }
  }

  /**
   * Detailed health check with component status
   */
  async getDetailedHealth(request, response) {
    try {
      const health = await this.checkDetailedHealth()
      
      const statusCode = health.status === 'healthy' ? 200 : 503
      
      response.writeHead(statusCode, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify(health, null, 2))
      
    } catch (error) {
      this.logger.error('Detailed health check failed', error)
      
      response.writeHead(500, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify({
        status: 'error',
        message: 'Detailed health check failed',
        timestamp: new Date().toISOString()
      }))
    }
  }

  /**
   * Readiness probe for Kubernetes
   */
  async getReadiness(request, response) {
    try {
      const ready = await this.checkReadiness()
      
      const statusCode = ready ? 200 : 503
      
      response.writeHead(statusCode, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify({
        status: ready ? 'ready' : 'not ready',
        timestamp: new Date().toISOString()
      }))
      
    } catch (error) {
      this.logger.error('Readiness check failed', error)
      
      response.writeHead(503, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify({
        status: 'not ready',
        message: 'Readiness check failed',
        timestamp: new Date().toISOString()
      }))
    }
  }

  /**
   * Liveness probe for Kubernetes
   */
  async getLiveness(request, response) {
    try {
      const alive = await this.checkLiveness()
      
      const statusCode = alive ? 200 : 503
      
      response.writeHead(statusCode, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify({
        status: alive ? 'alive' : 'not alive',
        timestamp: new Date().toISOString()
      }))
      
    } catch (error) {
      this.logger.error('Liveness check failed', error)
      
      response.writeHead(503, {
        'Content-Type': 'application/json',
        ...this.getCorsHeaders()
      })
      
      response.end(JSON.stringify({
        status: 'not alive',
        message: 'Liveness check failed',
        timestamp: new Date().toISOString()
      }))
    }
  }

  /**
   * Basic health check logic
   */
  async checkHealth() {
    const checks = []
    let overallStatus = 'healthy'

    // Check uptime
    const uptime = Date.now() - this.startTime
    checks.push({
      name: 'uptime',
      status: 'healthy',
      uptime: uptime,
      uptimeHuman: this.formatUptime(uptime)
    })

    // Check memory usage
    const memoryUsage = process.memoryUsage()
    const memoryStatus = memoryUsage.heapUsed < 1024 * 1024 * 1024 ? 'healthy' : 'warning' // 1GB threshold
    checks.push({
      name: 'memory',
      status: memoryStatus,
      heapUsed: memoryUsage.heapUsed,
      heapTotal: memoryUsage.heapTotal,
      external: memoryUsage.external
    })

    if (memoryStatus !== 'healthy') {
      overallStatus = 'degraded'
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '0.1.1',
      node: process.version,
      checks
    }
  }

  /**
   * Detailed health check with all components
   */
  async checkDetailedHealth() {
    const basicHealth = await this.checkHealth()
    const components = {}

    // Check connection manager
    if (this.connectionManager) {
      try {
        const stats = this.connectionManager.getConnectionStats()
        components.connectionManager = {
          status: 'healthy',
          connections: stats.totalConnections,
          documents: stats.documentCount,
          maxConnections: stats.maxConnections
        }
      } catch (error) {
        components.connectionManager = {
          status: 'unhealthy',
          error: error.message
        }
      }
    }

    // Check document manager
    if (this.documentManager) {
      try {
        const stats = this.documentManager.getAllDocumentStats()
        components.documentManager = {
          status: 'healthy',
          documents: stats.totalDocuments,
          connections: stats.totalConnections,
          messages: stats.totalMessages
        }
      } catch (error) {
        components.documentManager = {
          status: 'unhealthy',
          error: error.message
        }
      }
    }

    // Check persistence
    if (this.persistence) {
      try {
        const exists = await this.persistence.exists('health-check-test')
        components.persistence = {
          status: 'healthy',
          type: this.persistence.constructor.name
        }
      } catch (error) {
        components.persistence = {
          status: 'unhealthy',
          error: error.message
        }
      }
    }



    // Determine overall status
    const componentStatuses = Object.values(components).map(c => c.status)
    const hasUnhealthy = componentStatuses.includes('unhealthy')
    const hasWarning = componentStatuses.includes('warning')
    
    let overallStatus = basicHealth.status
    if (hasUnhealthy) {
      overallStatus = 'unhealthy'
    } else if (hasWarning) {
      overallStatus = 'degraded'
    }

    return {
      ...basicHealth,
      status: overallStatus,
      components
    }
  }

  /**
   * Check if service is ready to accept traffic
   */
  async checkReadiness() {
    try {
      // Service is ready if all critical components are working
      if (this.persistence) {
        // Test persistence connectivity
        await this.persistence.exists('readiness-check')
      }

      // Check if we can create documents
      if (this.documentManager) {
        this.documentManager.getDocumentCount()
      }

      return true
    } catch (error) {
      this.logger.error('Readiness check failed', error)
      return false
    }
  }

  /**
   * Check if service is alive (basic liveness)
   */
  async checkLiveness() {
    try {
      // Basic liveness check - can we respond to requests?
      const uptime = Date.now() - this.startTime
      return uptime > 0
    } catch (error) {
      this.logger.error('Liveness check failed', error)
      return false
    }
  }

  /**
   * Format uptime in human readable format
   */
  formatUptime(uptimeMs) {
    const seconds = Math.floor(uptimeMs / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  /**
   * Get CORS headers for health endpoints
   */
  getCorsHeaders() {
    return {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  }
}

export default HealthController
