/**
 * Authentication Provider interface following the Interface Segregation Principle
 * Defines the contract for authentication operations
 */
export class IAuthProvider {
  /**
   * Authenticate a request
   * @param {Object} request - HTTP request object
   * @returns {Promise<Object|null>} User object if authenticated, null otherwise
   */
  async authenticate(request) {
    throw new Error('Method not implemented')
  }

  /**
   * Authorize access to a document
   * @param {Object} user - User object from authentication
   * @param {string} docName - Document name
   * @param {string} action - Action being performed (read, write, etc.)
   * @returns {Promise<boolean>} True if authorized
   */
  async authorize(user, docName, action) {
    throw new Error('Method not implemented')
  }

  /**
   * Validate token
   * @param {string} token - Authentication token
   * @returns {Promise<Object|null>} User object if valid, null otherwise
   */
  async validateToken(token) {
    throw new Error('Method not implemented')
  }

  /**
   * Generate token for user
   * @param {Object} user - User object
   * @returns {Promise<string>} Generated token
   */
  async generateToken(user) {
    throw new Error('Method not implemented')
  }

  /**
   * Refresh token
   * @param {string} token - Current token
   * @returns {Promise<string|null>} New token if refresh successful, null otherwise
   */
  async refreshToken(token) {
    throw new Error('Method not implemented')
  }
}
