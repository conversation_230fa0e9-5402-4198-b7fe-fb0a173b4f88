/**
 * Persistence interface following the Dependency Inversion Principle
 * Defines the contract for document persistence operations
 */
export class IPersistence {
  /**
   * Bind state to a document
   * @param {string} docName - Document name
   * @param {Object} doc - Document instance
   */
  bindState(docName, doc) {
    throw new Error('Method not implemented')
  }

  /**
   * Write document state to persistent storage
   * @param {string} docName - Document name
   * @param {Object} doc - Document instance
   * @returns {Promise<void>}
   */
  async writeState(docName, doc) {
    throw new Error('Method not implemented')
  }

  /**
   * Read document state from persistent storage
   * @param {string} docName - Document name
   * @returns {Promise<Uint8Array|null>}
   */
  async readState(docName) {
    throw new Error('Method not implemented')
  }

  /**
   * Delete document state from persistent storage
   * @param {string} docName - Document name
   * @returns {Promise<void>}
   */
  async deleteState(docName) {
    throw new Error('Method not implemented')
  }

  /**
   * Check if document exists in persistent storage
   * @param {string} docName - Document name
   * @returns {Promise<boolean>}
   */
  async exists(docName) {
    throw new Error('Method not implemented')
  }

  /**
   * Get all document names
   * @returns {Promise<string[]>}
   */
  async getAllDocuments() {
    throw new Error('Method not implemented')
  }

  /**
   * Close the persistence provider
   * @returns {Promise<void>}
   */
  async close() {
    throw new Error('Method not implemented')
  }
}
