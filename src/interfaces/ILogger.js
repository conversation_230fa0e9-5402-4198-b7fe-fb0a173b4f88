/**
 * Logger interface following the Interface Segregation Principle
 * Defines the contract for logging operations
 */
export class ILogger {
  /**
   * Log an error message
   * @param {string} message - The error message
   * @param {Error|Object} [error] - Optional error object or metadata
   */
  error(message, error) {
    throw new Error('Method not implemented')
  }

  /**
   * Log a warning message
   * @param {string} message - The warning message
   * @param {Object} [metadata] - Optional metadata
   */
  warn(message, metadata) {
    throw new Error('Method not implemented')
  }

  /**
   * Log an info message
   * @param {string} message - The info message
   * @param {Object} [metadata] - Optional metadata
   */
  info(message, metadata) {
    throw new Error('Method not implemented')
  }

  /**
   * Log a debug message
   * @param {string} message - The debug message
   * @param {Object} [metadata] - Optional metadata
   */
  debug(message, metadata) {
    throw new Error('Method not implemented')
  }

  /**
   * Create a child logger with additional context
   * @param {Object} context - Additional context for the child logger
   * @returns {ILogger} Child logger instance
   */
  child(context) {
    throw new Error('Method not implemented')
  }
}
