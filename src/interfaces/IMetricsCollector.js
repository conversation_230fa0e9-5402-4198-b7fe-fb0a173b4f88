/**
 * Metrics Collector interface following the Single Responsibility Principle
 * Defines the contract for collecting and reporting metrics
 */
export class IMetricsCollector {
  /**
   * Increment a counter metric
   * @param {string} name - Metric name
   * @param {number} value - Value to increment by (default: 1)
   * @param {Object} labels - Optional labels for the metric
   */
  incrementCounter(name, value = 1, labels = {}) {
    throw new Error('Method not implemented')
  }

  /**
   * Set a gauge metric
   * @param {string} name - Metric name
   * @param {number} value - Gauge value
   * @param {Object} labels - Optional labels for the metric
   */
  setGauge(name, value, labels = {}) {
    throw new Error('Method not implemented')
  }

  /**
   * Record a histogram value
   * @param {string} name - Metric name
   * @param {number} value - Value to record
   * @param {Object} labels - Optional labels for the metric
   */
  recordHistogram(name, value, labels = {}) {
    throw new Error('Method not implemented')
  }

  /**
   * Start a timer for measuring duration
   * @param {string} name - Metric name
   * @param {Object} labels - Optional labels for the metric
   * @returns {Function} Function to call to end the timer
   */
  startTimer(name, labels = {}) {
    throw new Error('Method not implemented')
  }

  /**
   * Get current metrics in Prometheus format
   * @returns {Promise<string>} Metrics in Prometheus format
   */
  async getMetrics() {
    throw new Error('Method not implemented')
  }

  /**
   * Reset all metrics
   */
  reset() {
    throw new Error('Method not implemented')
  }

  /**
   * Register a custom metric
   * @param {string} name - Metric name
   * @param {string} type - Metric type (counter, gauge, histogram)
   * @param {string} help - Help text for the metric
   * @param {string[]} labelNames - Label names for the metric
   */
  registerMetric(name, type, help, labelNames = []) {
    throw new Error('Method not implemented')
  }
}
