/**
 * Document Manager interface following the Single Responsibility Principle
 * Defines the contract for Y.js document management
 */
export class IDocumentManager {
  /**
   * Get or create a document
   * @param {string} docName - Document name
   * @param {Object} options - Document options
   * @returns {Object} Document instance
   */
  getDocument(docName, options = {}) {
    throw new Error('Method not implemented')
  }

  /**
   * Check if document exists
   * @param {string} docName - Document name
   * @returns {boolean} True if document exists
   */
  hasDocument(docName) {
    throw new Error('Method not implemented')
  }

  /**
   * Remove a document
   * @param {string} docName - Document name
   * @returns {Promise<void>}
   */
  async removeDocument(docName) {
    throw new Error('Method not implemented')
  }

  /**
   * Get all document names
   * @returns {string[]} Array of document names
   */
  getAllDocumentNames() {
    throw new Error('Method not implemented')
  }

  /**
   * Get document statistics
   * @param {string} docName - Document name
   * @returns {Object|null} Document statistics or null if not found
   */
  getDocumentStats(docName) {
    throw new Error('Method not implemented')
  }

  /**
   * Clean up inactive documents
   * @param {number} maxIdleTime - Maximum idle time in milliseconds
   * @returns {Promise<string[]>} Array of cleaned up document names
   */
  async cleanupInactiveDocuments(maxIdleTime) {
    throw new Error('Method not implemented')
  }

  /**
   * Get total number of documents
   * @returns {number} Total document count
   */
  getDocumentCount() {
    throw new Error('Method not implemented')
  }

  /**
   * Set content initializer function
   * @param {Function} initializer - Function to initialize document content
   */
  setContentInitializer(initializer) {
    throw new Error('Method not implemented')
  }

  /**
   * Close all documents
   * @returns {Promise<void>}
   */
  async closeAllDocuments() {
    throw new Error('Method not implemented')
  }
}
