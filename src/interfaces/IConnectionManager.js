/**
 * Connection Manager interface following the Single Responsibility Principle
 * Defines the contract for WebSocket connection management
 */
export class IConnectionManager {
  /**
   * Add a new connection
   * @param {string} connectionId - Unique connection identifier
   * @param {Object} connection - WebSocket connection object
   * @param {Object} metadata - Connection metadata
   */
  addConnection(connectionId, connection, metadata) {
    throw new Error('Method not implemented')
  }

  /**
   * Remove a connection
   * @param {string} connectionId - Connection identifier
   */
  removeConnection(connectionId) {
    throw new Error('Method not implemented')
  }

  /**
   * Get a connection by ID
   * @param {string} connectionId - Connection identifier
   * @returns {Object|null} Connection object or null if not found
   */
  getConnection(connectionId) {
    throw new Error('Method not implemented')
  }

  /**
   * Get all connections for a document
   * @param {string} docName - Document name
   * @returns {Map<string, Object>} Map of connection ID to connection object
   */
  getDocumentConnections(docName) {
    throw new Error('Method not implemented')
  }

  /**
   * Get total number of connections
   * @returns {number} Total connection count
   */
  getConnectionCount() {
    throw new Error('Method not implemented')
  }

  /**
   * Check if connection exists
   * @param {string} connectionId - Connection identifier
   * @returns {boolean} True if connection exists
   */
  hasConnection(connectionId) {
    throw new Error('Method not implemented')
  }

  /**
   * Get connection metadata
   * @param {string} connectionId - Connection identifier
   * @returns {Object|null} Connection metadata or null if not found
   */
  getConnectionMetadata(connectionId) {
    throw new Error('Method not implemented')
  }

  /**
   * Update connection metadata
   * @param {string} connectionId - Connection identifier
   * @param {Object} metadata - New metadata
   */
  updateConnectionMetadata(connectionId, metadata) {
    throw new Error('Method not implemented')
  }

  /**
   * Close all connections
   */
  closeAllConnections() {
    throw new Error('Method not implemented')
  }
}
