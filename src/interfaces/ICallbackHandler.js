/**
 * Callback Handler interface following the Single Responsibility Principle
 * Defines the contract for handling document update callbacks
 */
export class ICallbackHandler {
  /**
   * Handle document update callback
   * @param {Object} doc - Document instance
   * @returns {Promise<void>}
   */
  async handleCallback(doc) {
    throw new Error('Method not implemented')
  }

  /**
   * Check if callback is configured
   * @returns {boolean} True if callback is configured
   */
  isCallbackSet() {
    throw new Error('Method not implemented')
  }

  /**
   * Set callback configuration
   * @param {Object} config - Callback configuration
   */
  setCallbackConfig(config) {
    throw new Error('Method not implemented')
  }

  /**
   * Get callback configuration
   * @returns {Object} Current callback configuration
   */
  getCallbackConfig() {
    throw new Error('Method not implemented')
  }

  /**
   * Test callback connectivity
   * @returns {Promise<boolean>} True if callback endpoint is reachable
   */
  async testCallback() {
    throw new Error('Method not implemented')
  }
}
