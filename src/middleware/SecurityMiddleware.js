import crypto from 'crypto'
import config from '../config/index.js'
import logger from '../services/Logger.js'

/**
 * Security middleware for WebSocket connections
 */
export class SecurityMiddleware {
  constructor(options = {}) {
    this.corsOrigin = options.corsOrigin || config.security.corsOrigin
    this.maxMessageSize = options.maxMessageSize || 1024 * 1024 // 1MB default
    this.allowedOrigins = this.parseCorsOrigin(this.corsOrigin)
    this.logger = logger.child({ component: 'SecurityMiddleware' })
  }

  parseCorsOrigin(origin) {
    if (origin === '*') {
      return ['*']
    }
    
    if (typeof origin === 'string') {
      return origin.split(',').map(o => o.trim())
    }
    
    if (Array.isArray(origin)) {
      return origin
    }
    
    return ['http://localhost:3000'] // Default fallback
  }

  /**
   * Validate WebSocket upgrade request
   */
  validateUpgradeRequest(request) {
    try {
      // Check origin
      if (!this.isOriginAllowed(request)) {
        this.logger.warn('Origin not allowed', { 
          origin: request.headers.origin,
          allowedOrigins: this.allowedOrigins 
        })
        return { valid: false, reason: 'Origin not allowed' }
      }

      // Validate WebSocket headers
      if (!this.validateWebSocketHeaders(request)) {
        this.logger.warn('Invalid WebSocket headers')
        return { valid: false, reason: 'Invalid WebSocket headers' }
      }

      // Check for required headers
      if (!this.validateRequiredHeaders(request)) {
        this.logger.warn('Missing required headers')
        return { valid: false, reason: 'Missing required headers' }
      }

      return { valid: true }
    } catch (error) {
      this.logger.error('Error validating upgrade request', error)
      return { valid: false, reason: 'Internal validation error' }
    }
  }

  isOriginAllowed(request) {
    const origin = request.headers.origin
    
    // If no origin header, allow (for non-browser clients)
    if (!origin) {
      return true
    }

    // Allow all origins
    if (this.allowedOrigins.includes('*')) {
      return true
    }

    // Check exact match
    if (this.allowedOrigins.includes(origin)) {
      return true
    }

    // Check wildcard patterns
    return this.allowedOrigins.some(allowed => {
      if (allowed.includes('*')) {
        const pattern = allowed.replace(/\*/g, '.*')
        const regex = new RegExp(`^${pattern}$`)
        return regex.test(origin)
      }
      return false
    })
  }

  validateWebSocketHeaders(request) {
    const { headers } = request
    
    // Check for WebSocket upgrade headers
    if (headers.upgrade?.toLowerCase() !== 'websocket') {
      return false
    }
    
    if (!headers.connection?.toLowerCase().includes('upgrade')) {
      return false
    }
    
    if (!headers['sec-websocket-key']) {
      return false
    }
    
    if (headers['sec-websocket-version'] !== '13') {
      return false
    }
    
    return true
  }

  validateRequiredHeaders(request) {
    const requiredHeaders = ['host', 'sec-websocket-key']
    
    return requiredHeaders.every(header => {
      return request.headers[header] !== undefined
    })
  }

  /**
   * Validate message content
   */
  validateMessage(message, connection) {
    try {
      // Check message size
      if (message.length > this.maxMessageSize) {
        this.logger.warn('Message too large', { 
          size: message.length,
          maxSize: this.maxMessageSize,
          connectionId: connection.id 
        })
        return { valid: false, reason: 'Message too large' }
      }

      // Basic binary data validation
      if (!this.isValidBinaryData(message)) {
        this.logger.warn('Invalid binary data format')
        return { valid: false, reason: 'Invalid binary data' }
      }

      return { valid: true }
    } catch (error) {
      this.logger.error('Error validating message', error)
      return { valid: false, reason: 'Message validation error' }
    }
  }

  isValidBinaryData(data) {
    // Basic validation for Y.js protocol messages
    if (!(data instanceof Uint8Array) && !Buffer.isBuffer(data)) {
      return false
    }

    // Check minimum message length
    if (data.length === 0) {
      return false
    }

    // Y.js messages should start with valid message type
    const messageType = data[0]
    return messageType >= 0 && messageType <= 2 // sync, awareness, auth
  }

  /**
   * Generate secure connection ID
   */
  generateConnectionId() {
    return crypto.randomBytes(16).toString('hex')
  }

  /**
   * Sanitize document name
   */
  sanitizeDocumentName(docName) {
    if (!docName || typeof docName !== 'string') {
      return null
    }

    // Remove potentially dangerous characters
    const sanitized = docName
      .replace(/[^a-zA-Z0-9\-_\.]/g, '')
      .substring(0, 255) // Limit length

    if (sanitized.length === 0) {
      return null
    }

    // Prevent path traversal
    if (sanitized.includes('..') || sanitized.startsWith('.')) {
      return null
    }

    return sanitized
  }

  /**
   * Extract and validate document name from URL
   */
  extractDocumentName(url) {
    try {
      const docName = url.slice(1).split('?')[0]
      return this.sanitizeDocumentName(docName)
    } catch (error) {
      this.logger.error('Error extracting document name', error)
      return null
    }
  }

  /**
   * Create security headers for HTTP responses
   */
  getSecurityHeaders() {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Content-Security-Policy': "default-src 'none'",
      'Referrer-Policy': 'no-referrer'
    }
  }
}

export default SecurityMiddleware
