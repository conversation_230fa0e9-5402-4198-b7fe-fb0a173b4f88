import config from '../config/index.js'
import logger from '../services/Logger.js'

/**
 * Rate limiter middleware for WebSocket connections
 */
export class RateLimiter {
  constructor(options = {}) {
    this.windowMs = options.windowMs || config.security.rateLimitWindowMs
    this.maxRequests = options.maxRequests || config.security.rateLimitMaxRequests
    this.keyGenerator = options.keyGenerator || this.defaultKeyGenerator
    this.skipSuccessfulRequests = options.skipSuccessfulRequests || false
    this.skipFailedRequests = options.skipFailedRequests || false
    this.logger = logger.child({ component: 'RateLimiter' })
    
    // Store for tracking requests
    this.store = new Map()
    
    // Cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, this.windowMs)
  }

  defaultKeyGenerator(request) {
    // Use IP address as default key
    return request.socket.remoteAddress || 
           request.headers['x-forwarded-for'] || 
           request.headers['x-real-ip'] || 
           'unknown'
  }

  isRateLimited(request) {
    const key = this.keyGenerator(request)
    const now = Date.now()
    const windowStart = now - this.windowMs

    // Get or create record for this key
    let record = this.store.get(key)
    if (!record) {
      record = {
        requests: [],
        resetTime: now + this.windowMs
      }
      this.store.set(key, record)
    }

    // Remove old requests outside the window
    record.requests = record.requests.filter(timestamp => timestamp > windowStart)

    // Check if limit exceeded
    if (record.requests.length >= this.maxRequests) {
      this.logger.warn('Rate limit exceeded', {
        key,
        requestCount: record.requests.length,
        maxRequests: this.maxRequests,
        windowMs: this.windowMs
      })
      return true
    }

    // Add current request
    record.requests.push(now)
    record.resetTime = now + this.windowMs

    return false
  }

  getRemainingRequests(request) {
    const key = this.keyGenerator(request)
    const record = this.store.get(key)
    
    if (!record) {
      return this.maxRequests
    }

    const now = Date.now()
    const windowStart = now - this.windowMs
    const validRequests = record.requests.filter(timestamp => timestamp > windowStart)
    
    return Math.max(0, this.maxRequests - validRequests.length)
  }

  getResetTime(request) {
    const key = this.keyGenerator(request)
    const record = this.store.get(key)
    
    return record ? record.resetTime : Date.now() + this.windowMs
  }

  cleanup() {
    const now = Date.now()
    const keysToDelete = []

    for (const [key, record] of this.store.entries()) {
      // Remove expired records
      if (record.resetTime < now) {
        keysToDelete.push(key)
      } else {
        // Clean up old requests within active records
        const windowStart = now - this.windowMs
        record.requests = record.requests.filter(timestamp => timestamp > windowStart)
        
        // If no requests left, mark for deletion
        if (record.requests.length === 0) {
          keysToDelete.push(key)
        }
      }
    }

    keysToDelete.forEach(key => this.store.delete(key))

    if (keysToDelete.length > 0) {
      this.logger.debug('Cleaned up rate limiter records', { 
        deletedKeys: keysToDelete.length,
        totalKeys: this.store.size 
      })
    }
  }

  reset(request) {
    const key = this.keyGenerator(request)
    this.store.delete(key)
    this.logger.debug('Rate limit reset for key', { key })
  }

  resetAll() {
    this.store.clear()
    this.logger.info('All rate limits reset')
  }

  getStats() {
    return {
      totalKeys: this.store.size,
      windowMs: this.windowMs,
      maxRequests: this.maxRequests
    }
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.store.clear()
  }
}

export default RateLimiter
