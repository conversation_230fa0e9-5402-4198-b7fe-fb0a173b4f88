#!/usr/bin/env node

import WebSocket from 'ws'
import http from 'http'
import url from 'url'
import config from './config/index.js'
import logger from './services/Logger.js'
import { ConnectionManager } from './services/ConnectionManager.js'
import { DocumentManager } from './services/DocumentManager.js'
import { WebSocketHandler } from './services/WebSocketHandler.js'
import { LevelDBPersistence } from './services/LevelDBPersistence.js'
import { JWTAuthProvider } from './services/JWTAuthProvider.js'

import { SecurityMiddleware } from './middleware/SecurityMiddleware.js'
import { RateLimiter } from './middleware/RateLimiter.js'

/**
 * Production-grade Y.js WebSocket Server
 * Following SOLID principles and best practices
 */
class YWebSocketServer {
  constructor() {
    this.server = null
    this.wss = null
    this.connectionManager = null
    this.documentManager = null
    this.webSocketHandler = null
    this.persistence = null
    this.authProvider = null

    this.securityMiddleware = null
    this.rateLimiter = null
    this.isShuttingDown = false
    
    this.logger = logger.child({ component: 'YWebSocketServer' })
  }

  async initialize() {
    try {
      this.logger.info('Initializing Y.js WebSocket Server', {
        nodeEnv: config.server.nodeEnv,
        host: config.server.host,
        port: config.server.port
      })

      // Initialize components
      await this.initializeComponents()
      
      // Create HTTP server
      this.createHttpServer()
      
      // Create WebSocket server
      this.createWebSocketServer()
      
      // Setup graceful shutdown
      this.setupGracefulShutdown()
      
      this.logger.info('Y.js WebSocket Server initialized successfully')
      
    } catch (error) {
      this.logger.error('Failed to initialize server', error)
      throw error
    }
  }

  async initializeComponents() {
    // Initialize security middleware
    this.securityMiddleware = new SecurityMiddleware()

    // Initialize rate limiter
    this.rateLimiter = new RateLimiter()

    // Initialize persistence if configured
    if (config.persistence.directory) {
      this.persistence = new LevelDBPersistence({
        directory: config.persistence.directory
      })
      await this.persistence.initialize()
    }

    // Initialize authentication provider if enabled
    if (config.auth.enabled) {
      this.authProvider = new JWTAuthProvider({
        secret: config.security.jwtSecret
      })
    }

    // Initialize connection manager
    this.connectionManager = new ConnectionManager({
      maxConnections: config.websocket.maxConnections,
      connectionTimeout: config.websocket.connectionTimeout
    })

    // Initialize document manager
    this.documentManager = new DocumentManager({
      persistence: this.persistence,
      gcEnabled: config.persistence.gcEnabled
    })

    // Initialize WebSocket handler
    this.webSocketHandler = new WebSocketHandler({
      connectionManager: this.connectionManager,
      documentManager: this.documentManager,
      authProvider: this.authProvider,
      securityMiddleware: this.securityMiddleware,
      pingTimeout: config.websocket.pingTimeout
    })


  }

  createHttpServer() {
    this.server = http.createServer((request, response) => {
      this.handleHttpRequest(request, response)
    })

    this.server.on('upgrade', (request, socket, head) => {
      this.handleWebSocketUpgrade(request, socket, head)
    })
  }

  createWebSocketServer() {
    this.wss = new WebSocket.Server({ 
      noServer: true,
      perMessageDeflate: {
        zlibDeflateOptions: {
          level: 3,
          chunkSize: 1024
        },
        threshold: 1024,
        concurrencyLimit: 10
      }
    })

    this.wss.on('connection', (ws, request) => {
      this.webSocketHandler.setupConnection(ws, request)
    })
  }

  handleHttpRequest(request, response) {
    const parsedUrl = url.parse(request.url, true)

    try {
      // Add security headers
      if (this.securityMiddleware) {
        const securityHeaders = this.securityMiddleware.getSecurityHeaders()
        Object.entries(securityHeaders).forEach(([key, value]) => {
          response.setHeader(key, value)
        })
      }

      // Handle CORS preflight
      if (request.method === 'OPTIONS') {
        this.handleCorsPreflightRequest(request, response)
        return
      }

      // Route requests
      switch (parsedUrl.pathname) {
        case '/':
          this.handleRootRequest(request, response)
          break
        default:
          this.handleNotFound(request, response)
          break
      }

    } catch (error) {
      this.logger.error('Error handling HTTP request', error)
      this.handleInternalError(request, response, error)
    }
  }

  handleWebSocketUpgrade(request, socket, head) {
    try {
      // Check rate limiting
      if (this.rateLimiter && this.rateLimiter.isRateLimited(request)) {
        socket.write('HTTP/1.1 429 Too Many Requests\r\n\r\n')
        socket.destroy()
        return
      }

      // Validate upgrade request
      if (this.securityMiddleware) {
        const validation = this.securityMiddleware.validateUpgradeRequest(request)
        if (!validation.valid) {
          this.logger.warn('WebSocket upgrade validation failed', {
            reason: validation.reason,
            origin: request.headers.origin
          })
          socket.write('HTTP/1.1 403 Forbidden\r\n\r\n')
          socket.destroy()
          return
        }
      }

      // Handle upgrade
      if (this.wss) {
        this.wss.handleUpgrade(request, socket, head, (ws) => {
          this.wss?.emit('connection', ws, request)
        })
      }

    } catch (error) {
      this.logger.error('Error handling WebSocket upgrade', error)
      socket.write('HTTP/1.1 500 Internal Server Error\r\n\r\n')
      socket.destroy()
    }
  }

  handleCorsPreflightRequest(request, response) {
    response.writeHead(200, {
      'Access-Control-Allow-Origin': config.security.corsOrigin,
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    })
    response.end()
  }



  handleRootRequest(request, response) {
    response.writeHead(200, { 'Content-Type': 'text/plain' })
    response.end('Y.js WebSocket Server - OK')
  }

  handleNotFound(request, response) {
    response.writeHead(404, { 'Content-Type': 'text/plain' })
    response.end('Not Found')
  }

  handleInternalError(request, response, error) {
    response.writeHead(500, { 'Content-Type': 'text/plain' })
    response.end('Internal Server Error')
  }

  async start() {
    return new Promise((resolve, reject) => {
      if (!this.server) {
        reject(new Error('Server not initialized'))
        return
      }

      this.server.listen(config.server.port, config.server.host, (error) => {
        if (error) {
          this.logger.error('Failed to start server', error)
          reject(error)
        } else {
          this.logger.info('Y.js WebSocket Server started', {
            host: config.server.host,
            port: config.server.port,
            pid: process.pid
          })
          resolve(undefined)
        }
      })
    })
  }

  setupGracefulShutdown() {
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2']
    
    signals.forEach(signal => {
      process.on(signal, () => {
        this.logger.info(`Received ${signal}, starting graceful shutdown`)
        this.gracefulShutdown()
      })
    })
  }

  async gracefulShutdown() {
    if (this.isShuttingDown) {
      this.logger.warn('Shutdown already in progress')
      return
    }

    this.isShuttingDown = true
    
    try {
      this.logger.info('Starting graceful shutdown')
      
      // Stop accepting new connections
      if (this.server) {
        this.server.close()
      }
      
      // Close all WebSocket connections
      this.connectionManager?.closeAllConnections()
      
      // Close all documents
      await this.documentManager?.closeAllDocuments()
      
      // Close persistence
      await this.persistence?.close()
      
      // Cleanup components
      this.rateLimiter?.destroy()
      this.connectionManager?.destroy()
      this.documentManager?.destroy()
      
      this.logger.info('Graceful shutdown completed')
      process.exit(0)
      
    } catch (error) {
      this.logger.error('Error during graceful shutdown', error)
      process.exit(1)
    }
  }
}

// Start the server
async function main() {
  try {
    const server = new YWebSocketServer()
    await server.initialize()
    await server.start()
  } catch (error) {
    logger.error('Failed to start Y.js WebSocket Server', error)
    process.exit(1)
  }
}

// Only run if this file is executed directly
main()

export { YWebSocketServer }
export default YWebSocketServer
