# Server Configuration
NODE_ENV=development
HOST=localhost
PORT=1234

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# WebSocket Configuration
WS_PING_TIMEOUT=30000
WS_MAX_CONNECTIONS=1000
WS_CONNECTION_TIMEOUT=60000

# Persistence Configuration
YPERSISTENCE=./data
GC=true

# Callback Configuration
CALLBACK_URL=
CALLBACK_DEBOUNCE_WAIT=2000
CALLBACK_DEBOUNCE_MAXWAIT=10000
CALLBACK_TIMEOUT=5000
CALLBACK_OBJECTS={}

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# Database Configuration (if using external persistence)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yjs_websocket
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# Redis Configuration (for scaling)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Authentication Configuration
AUTH_ENABLED=false
AUTH_PROVIDER=jwt
AUTH_HEADER=authorization
