{"name": "@y/websocket-server", "version": "0.1.1", "description": "Backend for y-websocket", "type": "module", "sideEffects": false, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}, "scripts": {"start": "node ./src/server.js", "dev": "NODE_ENV=development node ./src/server.js", "prod": "NODE_ENV=production node ./src/server.js", "dist": "rm -rf dist && rollup -c && tsc", "lint": "standard && tsc", "test": "npm run lint", "test:unit": "echo 'Unit tests not implemented yet'", "test:integration": "echo 'Integration tests not implemented yet'", "docker:build": "docker build -t y-websocket-server .", "docker:run": "docker run -p 1234:1234 y-websocket-server", "docker:compose": "docker-compose up -d", "docker:compose:down": "docker-compose down", "logs": "tail -f ./logs/app.log", "health": "curl -f http://localhost:1234/health || exit 1", "preversion": "npm run lint && npm run dist && test -e dist/src/server.d.ts && test -e dist/server.cjs"}, "bin": {"y-websocket-server": "src/server.js", "y-websocket": "src/server.js"}, "files": ["dist/*", "src/*"], "exports": {"./package.json": "./package.json", "./utils": {"import": "./src/utils.js", "require": "./dist/utils.cjs", "types": "./dist/src/utils.d.ts", "default": "./src/utils.js"}, "./callback": {"import": "./src/callback.js", "require": "./dist/callback.cjs", "types": "./dist/src/callback.d.ts", "default": "./src/callback.js"}}, "repository": {"type": "git", "url": "git+https://github.com/yjs/y-websocket-server.git"}, "keywords": ["<PERSON><PERSON><PERSON>"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/yjs/y-websocket-server/issues"}, "homepage": "https://github.com/yjs/y-websocket-server#readme", "standard": {"ignore": ["/dist", "/node_modules"]}, "dependencies": {"@tiptap/extension-document": "^2.24.2", "@tiptap/extension-paragraph": "^2.24.2", "@tiptap/extension-text": "^2.24.2", "@y/protocols": "^1.0.6-1", "cors": "^2.8.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "level": "^8.0.0", "lib0": "^0.2.102", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^24.0.1", "@types/ws": "^8.5.10", "rollup": "^4.39.0", "standard": "^17.1.2", "typescript": "^5.8.3", "ws": "^6.2.1", "yjs": "^14.0.0-7"}, "peerDependencies": {"yjs": "^14.0.0-7"}, "engines": {"npm": ">=8.0.0", "node": ">=16.0.0"}}