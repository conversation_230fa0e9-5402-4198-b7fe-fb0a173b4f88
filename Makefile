# Y.js WebSocket Server Makefile
# Production-grade development and deployment automation

.PHONY: help install dev prod build test lint clean docker-build docker-run docker-compose-up docker-compose-down logs health setup-env backup restore

# Default target
.DEFAULT_GOAL := help

# Variables
NODE_ENV ?= development
PORT ?= 1234
HOST ?= localhost
DOCKER_IMAGE_NAME ?= y-websocket-server
DOCKER_TAG ?= latest
BACKUP_DIR ?= ./backups

# Colors for output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

help: ## Show this help message
	@echo "$(BLUE)Y.js WebSocket Server - Available Commands$(RESET)"
	@echo ""
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

# Development Commands
install: ## Install dependencies
	@echo "$(BLUE)Installing dependencies...$(RESET)"
	npm ci
	@echo "$(GREEN)Dependencies installed successfully$(RESET)"

dev: ## Start development server
	@echo "$(BLUE)Starting development server...$(RESET)"
	NODE_ENV=development npm run dev

prod: ## Start production server
	@echo "$(BLUE)Starting production server...$(RESET)"
	NODE_ENV=production npm run prod

build: ## Build the project
	@echo "$(BLUE)Building project...$(RESET)"
	npm run dist
	@echo "$(GREEN)Build completed$(RESET)"

# Testing and Quality
test: ## Run all tests
	@echo "$(BLUE)Running tests...$(RESET)"
	npm test

test-unit: ## Run unit tests
	@echo "$(BLUE)Running unit tests...$(RESET)"
	npm run test:unit

test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(RESET)"
	npm run test:integration

lint: ## Run linter
	@echo "$(BLUE)Running linter...$(RESET)"
	npm run lint

lint-fix: ## Fix linting issues
	@echo "$(BLUE)Fixing linting issues...$(RESET)"
	npx standard --fix

# Environment Setup
setup-env: ## Setup environment files
	@echo "$(BLUE)Setting up environment files...$(RESET)"
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(GREEN)Created .env file from .env.example$(RESET)"; \
		echo "$(YELLOW)Please edit .env file with your configuration$(RESET)"; \
	else \
		echo "$(YELLOW).env file already exists$(RESET)"; \
	fi
	@mkdir -p logs data backups
	@echo "$(GREEN)Created necessary directories$(RESET)"

# Docker Commands
docker-build: ## Build Docker image
	@echo "$(BLUE)Building Docker image...$(RESET)"
	docker build -t $(DOCKER_IMAGE_NAME):$(DOCKER_TAG) .
	@echo "$(GREEN)Docker image built successfully$(RESET)"

docker-build-dev: ## Build Docker image for development
	@echo "$(BLUE)Building Docker development image...$(RESET)"
	docker build --target development -t $(DOCKER_IMAGE_NAME):dev .
	@echo "$(GREEN)Docker development image built successfully$(RESET)"

docker-run: ## Run Docker container
	@echo "$(BLUE)Running Docker container...$(RESET)"
	docker run -d \
		--name $(DOCKER_IMAGE_NAME) \
		-p $(PORT):1234 \
		-v $(PWD)/data:/app/data \
		-v $(PWD)/logs:/app/logs \
		--env-file .env \
		$(DOCKER_IMAGE_NAME):$(DOCKER_TAG)
	@echo "$(GREEN)Docker container started$(RESET)"

docker-stop: ## Stop Docker container
	@echo "$(BLUE)Stopping Docker container...$(RESET)"
	docker stop $(DOCKER_IMAGE_NAME) || true
	docker rm $(DOCKER_IMAGE_NAME) || true
	@echo "$(GREEN)Docker container stopped$(RESET)"

docker-compose-up: ## Start services with docker-compose
	@echo "$(BLUE)Starting services with docker-compose...$(RESET)"
	docker-compose up -d
	@echo "$(GREEN)Services started$(RESET)"

docker-compose-down: ## Stop services with docker-compose
	@echo "$(BLUE)Stopping services with docker-compose...$(RESET)"
	docker-compose down
	@echo "$(GREEN)Services stopped$(RESET)"

docker-compose-logs: ## View docker-compose logs
	docker-compose logs -f

# Monitoring and Maintenance
logs: ## View application logs
	@if [ -f ./logs/app.log ]; then \
		tail -f ./logs/app.log; \
	else \
		echo "$(YELLOW)Log file not found. Starting server first...$(RESET)"; \
	fi

health: ## Check server health
	@echo "$(BLUE)Checking server health...$(RESET)"
	@curl -f http://$(HOST):$(PORT)/health || (echo "$(RED)Health check failed$(RESET)" && exit 1)
	@echo "$(GREEN)Server is healthy$(RESET)"

health-detailed: ## Check detailed server health
	@echo "$(BLUE)Checking detailed server health...$(RESET)"
	@curl -s http://$(HOST):$(PORT)/health/detailed | jq . || curl -s http://$(HOST):$(PORT)/health/detailed

metrics: ## View server metrics
	@echo "$(BLUE)Fetching server metrics...$(RESET)"
	@curl -s http://$(HOST):$(PORT)/metrics

# Backup and Restore
backup: ## Create backup of data
	@echo "$(BLUE)Creating backup...$(RESET)"
	@mkdir -p $(BACKUP_DIR)
	@tar -czf $(BACKUP_DIR)/backup-$(shell date +%Y%m%d-%H%M%S).tar.gz data logs
	@echo "$(GREEN)Backup created in $(BACKUP_DIR)$(RESET)"

restore: ## Restore from backup (usage: make restore BACKUP_FILE=backup.tar.gz)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "$(RED)Please specify BACKUP_FILE. Usage: make restore BACKUP_FILE=backup.tar.gz$(RESET)"; \
		exit 1; \
	fi
	@echo "$(BLUE)Restoring from $(BACKUP_FILE)...$(RESET)"
	@tar -xzf $(BACKUP_DIR)/$(BACKUP_FILE)
	@echo "$(GREEN)Restore completed$(RESET)"

# Cleanup
clean: ## Clean up generated files and dependencies
	@echo "$(BLUE)Cleaning up...$(RESET)"
	rm -rf node_modules
	rm -rf dist
	rm -rf logs/*.log
	npm cache clean --force
	@echo "$(GREEN)Cleanup completed$(RESET)"

clean-docker: ## Clean up Docker images and containers
	@echo "$(BLUE)Cleaning up Docker resources...$(RESET)"
	docker stop $(DOCKER_IMAGE_NAME) 2>/dev/null || true
	docker rm $(DOCKER_IMAGE_NAME) 2>/dev/null || true
	docker rmi $(DOCKER_IMAGE_NAME):$(DOCKER_TAG) 2>/dev/null || true
	docker system prune -f
	@echo "$(GREEN)Docker cleanup completed$(RESET)"

# Deployment
deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging...$(RESET)"
	@echo "$(YELLOW)Staging deployment not implemented yet$(RESET)"

deploy-production: ## Deploy to production environment
	@echo "$(BLUE)Deploying to production...$(RESET)"
	@echo "$(YELLOW)Production deployment not implemented yet$(RESET)"

# Quick start
quick-start: setup-env install ## Quick start for new developers
	@echo "$(GREEN)Quick start completed!$(RESET)"
	@echo "$(BLUE)Next steps:$(RESET)"
	@echo "  1. Edit .env file with your configuration"
	@echo "  2. Run 'make dev' to start development server"
	@echo "  3. Run 'make health' to check server status"

# Development workflow
dev-setup: clean install setup-env build ## Complete development setup
	@echo "$(GREEN)Development environment setup completed!$(RESET)"
