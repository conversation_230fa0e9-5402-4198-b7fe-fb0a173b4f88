-- Initialize database for Y.js WebSocket Server development
-- This script sets up the basic database structure if needed

-- Create database if it doesn't exist (PostgreSQL)
-- Note: This is mainly for documentation as the database is created by the container

-- Create a simple table for storing document metadata (optional)
CREATE TABLE IF NOT EXISTS document_metadata (
    id SERIAL PRIMARY KEY,
    document_name VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    size_bytes INTEGER DEFAULT 0,
    connection_count INTEGER DEFAULT 0
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_document_name ON document_metadata(document_name);

-- Insert some sample data for development
INSERT INTO document_metadata (document_name, size_bytes, connection_count) 
VALUES 
    ('sample-document', 1024, 0),
    ('test-document', 2048, 0)
ON CONFLICT (document_name) DO NOTHING;
