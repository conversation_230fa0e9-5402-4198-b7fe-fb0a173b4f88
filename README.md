
# Y.js WebSocket Server - Production Grade :rocket:

> **Production-ready, scalable, and secure WebSocket server for [Y.js](https://github.com/yjs/yjs) collaborative editing**

This is a completely refactored and enhanced version of the original y-websocket-server, built following **SOLID principles** with enterprise-grade features including comprehensive logging, security, monitoring, and Docker support.

## ✨ Features

- 🏗️ **SOLID Architecture**: Clean, maintainable code following SOLID principles
- 🔒 **Security**: JWT authentication, rate limiting, input validation, CORS protection
- 📊 **Monitoring**: Health checks, detailed logging with Winston
- 🐳 **Docker Ready**: Multi-stage Dockerfile with production optimizations
- 🔄 **Persistence**: LevelDB integration with automatic cleanup
- ⚡ **Performance**: Connection pooling, efficient memory management
- 🛡️ **Production Ready**: Graceful shutdown, error handling, security headers
- 📝 **Comprehensive Logging**: Structured JSON logging with multiple levels
- 🔧 **Configuration**: Environment-based configuration with validation

## 🚀 Quick Start

### Prerequisites

- Node.js 16+
- npm 8+

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd y-websocket-server

# Install dependencies
npm install

# Setup environment
make setup-env

# Edit .env file with your configuration
nano .env

# Start development server
make dev
```

### Using Docker

```bash
# Build and run with Docker
make docker-build
make docker-run

# Or use docker-compose
make docker-compose-up
```

## 📋 Configuration

The server uses environment variables for configuration. Copy `.env.example` to `.env` and customize:

### Core Settings
```env
NODE_ENV=production
HOST=0.0.0.0
PORT=1234
```

### Security
```env
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_MAX_REQUESTS=100
AUTH_ENABLED=true
```

### Persistence
```env
YPERSISTENCE=./data
GC=true
```

### Logging
```env
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=./logs/app.log
```

See `.env.example` for all available options.

## 🏗️ Architecture

The server follows SOLID principles with clear separation of concerns:

```
src/
├── config/           # Configuration management
├── interfaces/       # TypeScript-style interfaces for contracts
├── services/         # Core business logic
│   ├── Logger.js           # Winston-based logging
│   ├── ConnectionManager.js # WebSocket connection management
│   ├── DocumentManager.js  # Y.js document lifecycle
│   ├── WebSocketHandler.js # WebSocket message handling
│   ├── LevelDBPersistence.js # Data persistence
│   └── JWTAuthProvider.js  # Authentication
├── middleware/       # Request/response middleware
│   ├── SecurityMiddleware.js # Security validations
│   └── RateLimiter.js       # Rate limiting
├── controllers/      # HTTP request handlers
│   └── HealthController.js  # Health check endpoints
└── server-new.js     # Main server application
```

### Key Components

- **ConnectionManager**: Manages WebSocket connections with health monitoring
- **DocumentManager**: Handles Y.js document lifecycle and cleanup
- **SecurityMiddleware**: Validates requests, handles CORS, input sanitization
- **LevelDBPersistence**: Provides document persistence with backup/restore
- **HealthController**: Comprehensive health checks for monitoring

## 🔌 API Endpoints

### Health Monitoring
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed component health
- `GET /health/ready` - Kubernetes readiness probe
- `GET /health/live` - Kubernetes liveness probe

### WebSocket
- `WS /document-name` - Y.js document collaboration endpoint

## 🛠️ Development

### Available Commands

```bash
# Development
make dev              # Start development server
make install          # Install dependencies
make lint             # Run linter
make test             # Run tests

# Docker
make docker-build     # Build Docker image
make docker-run       # Run Docker container
make docker-compose-up # Start with docker-compose

# Monitoring
make health           # Check server health
make logs             # View application logs

# Maintenance
make backup           # Create data backup
make clean            # Clean generated files
```

### Testing the Server

```bash
# Check if server is running
curl http://localhost:1234/health

# Test WebSocket connection (requires wscat)
wscat -c ws://localhost:1234/test-document
```

## 🐳 Docker Deployment

### Production Deployment

```bash
# Build production image
docker build -t y-websocket-server .

# Run with environment file
docker run -d \
  --name y-websocket-server \
  -p 1234:1234 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  y-websocket-server
```

### Docker Compose

```bash
# Production
docker-compose up -d

# Development with additional services
docker-compose -f local.yaml up -d
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Configurable request rate limiting
- **Input Validation**: Comprehensive input sanitization
- **CORS Protection**: Configurable CORS policies
- **Security Headers**: Automatic security headers
- **Connection Limits**: Maximum connection enforcement

## 📊 Monitoring & Logging

### Logging
- **Structured Logging**: JSON format with metadata
- **Multiple Levels**: Error, warn, info, debug
- **File Rotation**: Automatic log file rotation
- **Context Logging**: Component-specific log contexts

### Health Checks
- **Basic Health**: Uptime and memory usage
- **Component Health**: Individual component status
- **Kubernetes Probes**: Ready/live endpoints

## 🔄 Persistence

### LevelDB Integration
- **Automatic Persistence**: Documents saved automatically
- **Cleanup**: Inactive document cleanup
- **Backup/Restore**: Built-in backup functionality
- **Statistics**: Persistence usage statistics

## 🚀 Production Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure secure `JWT_SECRET`
3. Set appropriate `CORS_ORIGIN`
4. Configure persistence directory
5. Set up log rotation
6. Configure monitoring

### Kubernetes Deployment
The server includes health check endpoints suitable for Kubernetes:

```yaml
livenessProbe:
  httpGet:
    path: /health/live
    port: 1234
readinessProbe:
  httpGet:
    path: /health/ready
    port: 1234
```

## 🔧 Migration from Original Server

To migrate from the original server:

1. **Backup Data**: Backup your existing persistence data
2. **Update Configuration**: Convert environment variables to new format
3. **Test**: Run in development mode first
4. **Deploy**: Use the new production-ready server

The new server maintains compatibility with existing Y.js clients.

## 📈 Performance

- **Connection Pooling**: Efficient WebSocket connection management
- **Memory Management**: Automatic cleanup of inactive documents
- **Optimized Docker**: Multi-stage builds for smaller images
- **Graceful Shutdown**: Proper cleanup on termination

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the existing code style
4. Add tests for new features
5. Submit a pull request

## 📄 License

[The MIT License](./LICENSE) © Kevin Jahns

---

## 🆕 What's New in This Version

- **Complete Refactor**: Built from ground up following SOLID principles
- **Production Ready**: Enterprise-grade features and monitoring
- **Security First**: Comprehensive security features
- **Docker Support**: Full containerization with multi-stage builds
- **Monitoring**: Health checks and structured logging
- **Maintainable**: Clean architecture with clear separation of concerns
