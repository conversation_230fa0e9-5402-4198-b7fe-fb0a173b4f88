import React, { useState } from 'react'
import CollaborativeEditor from './components/CollaborativeEditor'
import UserSetup from './components/UserSetup'
import './App.css'

interface User {
  name: string
  color: string
  documentId: string
}

function App() {
  const [user, setUser] = useState<User | null>(null)

  const handleUserSetup = (name: string, color: string, documentId: string) => {
    setUser({ name, color, documentId })
  }

  const handleBackToSetup = () => {
    setUser(null)
  }

  return (
    <div className="App">
      {!user ? (
        <UserSetup onUserSetup={handleUserSetup} />
      ) : (
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Tiptap Collaborative Editor
                </h1>
                <p className="text-gray-600">
                  Real-time collaborative editing with Y.js WebSocket
                </p>
              </div>
              <button
                onClick={handleBackToSetup}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Change User
              </button>
            </div>

            {/* Editor */}
            <CollaborativeEditor
              documentId={user.documentId}
              userName={user.name}
              userColor={user.color}
              serverUrl="ws://localhost:1234"
            />

            {/* Instructions */}
            <div className="mt-6 bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">
                How to test collaboration:
              </h2>
              <ol className="list-decimal list-inside space-y-2 text-gray-700">
                <li>Open this app in multiple browser tabs or windows</li>
                <li>Use the same document ID: <code className="bg-gray-100 px-2 py-1 rounded text-sm">{user.documentId}</code></li>
                <li>Choose different names and colors for each user</li>
                <li>Start typing in any tab to see real-time synchronization!</li>
              </ol>

              <div className="mt-4 p-4 bg-blue-50 rounded-md">
                <h3 className="font-medium text-blue-900 mb-2">Features to try:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Bold, italic, and strikethrough formatting</li>
                  <li>• Headings and paragraphs</li>
                  <li>• Bullet and numbered lists</li>
                  <li>• See other users' cursors in real-time</li>
                  <li>• Automatic conflict resolution</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default App
