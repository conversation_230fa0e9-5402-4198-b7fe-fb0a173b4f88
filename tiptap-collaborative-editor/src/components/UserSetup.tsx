import React, { useState } from 'react'

interface UserSetupProps {
  onUserSetup: (name: string, color: string, documentId: string) => void
}

const UserSetup: React.FC<UserSetupProps> = ({ onUserSetup }) => {
  const [name, setName] = useState('')
  const [selectedColor, setSelectedColor] = useState('#3b82f6')
  const [documentId, setDocumentId] = useState('demo-document')

  const colors = [
    '#3b82f6', // Blue
    '#ef4444', // Red
    '#10b981', // Green
    '#f59e0b', // Yellow
    '#8b5cf6', // Purple
    '#06b6d4', // Cyan
    '#f97316', // Orange
    '#84cc16', // Lime
    '#ec4899', // Pink
    '#6b7280', // Gray
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (name.trim() && documentId.trim()) {
      onUserSetup(name.trim(), selectedColor, documentId.trim())
    }
  }

  const generateRandomDocumentId = () => {
    const randomId = Math.random().toString(36).substring(2, 15)
    setDocumentId(`doc-${randomId}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Collaborative Editor
          </h1>
          <p className="text-gray-600">
            Enter your details to start collaborating
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name Input */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Your Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter your name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          {/* Color Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Choose Your Color
            </label>
            <div className="grid grid-cols-5 gap-2">
              {colors.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setSelectedColor(color)}
                  className={`w-10 h-10 rounded-full border-2 transition-all ${
                    selectedColor === color
                      ? 'border-gray-800 scale-110'
                      : 'border-gray-300 hover:border-gray-500'
                  }`}
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
            <div className="mt-2 text-sm text-gray-500">
              Selected: {selectedColor}
            </div>
          </div>

          {/* Document ID */}
          <div>
            <label htmlFor="documentId" className="block text-sm font-medium text-gray-700 mb-2">
              Document ID
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                id="documentId"
                value={documentId}
                onChange={(e) => setDocumentId(e.target.value)}
                placeholder="Enter document ID"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
              <button
                type="button"
                onClick={generateRandomDocumentId}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm"
                title="Generate random document ID"
              >
                🎲
              </button>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Share this ID with others to collaborate on the same document
            </p>
          </div>

          {/* Preview */}
          <div className="bg-gray-50 rounded-md p-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Preview</h3>
            <div className="flex items-center space-x-3">
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                style={{ backgroundColor: selectedColor }}
              >
                {name.charAt(0).toUpperCase() || '?'}
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900">
                  {name || 'Your Name'}
                </div>
                <div className="text-xs text-gray-500">
                  Document: {documentId}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!name.trim() || !documentId.trim()}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Start Collaborating
          </button>
        </form>

        {/* Info Section */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 mb-2">How it works:</h3>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Enter your name and pick a color</li>
            <li>• Choose or create a document ID</li>
            <li>• Share the document ID with others</li>
            <li>• Start editing together in real-time!</li>
          </ul>
        </div>

        {/* Server Status */}
        <div className="mt-4 p-3 bg-blue-50 rounded-md">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs text-blue-800">
              Server: ws://localhost:1234
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserSetup
