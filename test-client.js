#!/usr/bin/env node

/**
 * Simple test client for Y.js WebSocket Server
 * This script tests basic WebSocket connectivity and Y.js document synchronization
 */

import WebSocket from 'ws'
import * as Y from 'yjs'

const SERVER_URL = process.env.WS_URL || 'ws://localhost:1234'
const DOCUMENT_NAME = process.env.DOC_NAME || 'test-document'

console.log(`Testing Y.js WebSocket Server at ${SERVER_URL}/${DOCUMENT_NAME}`)

// Create Y.js document
const doc = new Y.Doc()
const text = doc.getText('content')

// Add some initial content
text.insert(0, 'Hello from test client!')

// Connect to WebSocket server
const ws = new WebSocket(`${SERVER_URL}/${DOCUMENT_NAME}`)

ws.on('open', () => {
  console.log('✅ WebSocket connection established')
  
  // Send initial sync
  const syncMessage = new Uint8Array([0, 0, 1, 0]) // Basic sync message
  ws.send(syncMessage)
  
  // Add more content after connection
  setTimeout(() => {
    text.insert(text.length, '\nAdding more content after connection!')
    console.log('📝 Added content to document')
  }, 1000)
  
  // Close connection after 5 seconds
  setTimeout(() => {
    console.log('🔌 Closing connection')
    ws.close()
  }, 5000)
})

ws.on('message', (data) => {
  console.log('📨 Received message from server:', data.length, 'bytes')
})

ws.on('close', (code, reason) => {
  console.log(`🔌 Connection closed: ${code} ${reason}`)
  process.exit(0)
})

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error.message)
  process.exit(1)
})

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Terminating test client')
  ws.close()
  process.exit(0)
})
